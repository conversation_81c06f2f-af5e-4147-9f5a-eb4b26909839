package com.swiftsku.swiftpos.di.domain.dispenser

import com.swiftsku.fdc.core.di.usecases.core.FQMMessageListenerUseCase
import com.swiftsku.fdc.core.di.usecases.core.GetVersionInfoUseCase
import com.swiftsku.fdc.core.di.usecases.core.ListenForFDCExceptionUseCase
import com.swiftsku.fdc.core.di.usecases.core.ListenForFDCReadyUseCase
import com.swiftsku.fdc.core.di.usecases.core.LogInUseCase
import com.swiftsku.fdc.core.di.usecases.core.SendPOSReadyUseCase
import com.swiftsku.fdc.core.di.usecases.core.StartFDCUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ChangeFuelPriceRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ClearFuelSaleRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.FreeFuelPointUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.GetCurrentFuelingStatusUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.GetDSPConfigurationUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.GetFPStateRequestUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ListenForFPStateChangeMessageUseCase
import com.swiftsku.fdc.core.di.usecases.dispenser.ListenForFuelSaleTransactionsMessageUseCase
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.domain.dispenser.FDCHeartBeatInitializeUseCase
import com.swiftsku.swiftpos.domain.dispenser.GetDashboardFuelPumpsUseCase
import com.swiftsku.swiftpos.domain.dispenser.SetupFDCLogOnUseCase
import com.swiftsku.swiftpos.domain.dispenser.SetupFPStateChangeListenerUseCase
import com.swiftsku.swiftpos.domain.dispenser.SetupChangeFuelPriceListenerUseCase
import com.swiftsku.swiftpos.domain.dispenser.SetupFuelSaleTransactionsListenerUseCase
import com.swiftsku.swiftpos.domain.dispenser.UpdateDSPStateWithFPStateUseCase
import com.swiftsku.swiftpos.domain.dispenser.UpdateDispenserStateWithCurrentFuelingStatusUseCase
import com.swiftsku.swiftpos.domain.dispenser.UpdateFuelSaleTransactionStatusUseCase
import com.swiftsku.swiftpos.domain.dispenser.ValidateFuelPriceUseCase
import com.swiftsku.swiftpos.domain.dispenser.WatchPumpUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DispenserUseCaseModule {
    @Provides
    @Singleton
    fun providesWatchPumpUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        getCurrentFuelingStatus: GetCurrentFuelingStatusUseCase
    ): WatchPumpUseCase = WatchPumpUseCase(dispatcher, getCurrentFuelingStatus)

    @Provides
    @Singleton
    fun providesGetDashboardFuelPumpsUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        getDSPConfigurationUseCase: GetDSPConfigurationUseCase,
        update: UpdateDispenserStateWithCurrentFuelingStatusUseCase,
        updateFPState: UpdateDSPStateWithFPStateUseCase,
    ): GetDashboardFuelPumpsUseCase =
        GetDashboardFuelPumpsUseCase(
            dispatcher,
            getDSPConfigurationUseCase,
            update,
            updateFPState,
        )

    @Provides
    @Singleton
    fun providesUpdateDispenserStateWithCurrentFuelingStatusUseCase(
        getDSPConfigurationUseCase: GetCurrentFuelingStatusUseCase,
        @IODispatcher dispatcher: CoroutineDispatcher
    ): UpdateDispenserStateWithCurrentFuelingStatusUseCase =
        UpdateDispenserStateWithCurrentFuelingStatusUseCase(dispatcher, getDSPConfigurationUseCase)


    @Provides
    @Singleton
    fun providesUpdateDSPStateWithFPStateUseCase(
        getDSPConfigurationUseCase: GetFPStateRequestUseCase,
        @IODispatcher dispatcher: CoroutineDispatcher,
    ): UpdateDSPStateWithFPStateUseCase =
        UpdateDSPStateWithFPStateUseCase(dispatcher, getDSPConfigurationUseCase)

    @Provides
    @Singleton
    fun providesFDCHeartBeatInitializeUseCase(
        fDCReadyUseCase: ListenForFDCReadyUseCase,
        posReady: SendPOSReadyUseCase,
        fqmMessageListener: FQMMessageListenerUseCase
    ): FDCHeartBeatInitializeUseCase =
        FDCHeartBeatInitializeUseCase(fDCReadyUseCase, posReady, fqmMessageListener)

    @Provides
    @Singleton
    fun providesSetupFPStateChangeListenerUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        listenerForFPStateChangeMessageUseCase: ListenForFPStateChangeMessageUseCase,
    ): SetupFPStateChangeListenerUseCase =
        SetupFPStateChangeListenerUseCase(
            dispatcher,
            listenerForFPStateChangeMessageUseCase,
        )

    @Provides
    @Singleton
    fun providesSetupFuelSaleTransactionsListenerUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        listenerForFPStateChangeMessageUseCase: ListenForFuelSaleTransactionsMessageUseCase,
        clear: ClearFuelSaleRequestUseCase,
        freeFP: FreeFuelPointUseCase,
        update: UpdateFuelSaleTransactionStatusUseCase
    ): SetupFuelSaleTransactionsListenerUseCase =
        SetupFuelSaleTransactionsListenerUseCase(
            dispatcher,
            listenerForFPStateChangeMessageUseCase,
            clear,
            freeFP,
            update
        )

    @Provides
    @Singleton
    fun providesSetupFuelPriceChangeListenerUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        getDashboardFuelPumpsUseCase: GetDashboardFuelPumpsUseCase,
        changeFuelPriceRequestUseCase: ChangeFuelPriceRequestUseCase,
    ): SetupChangeFuelPriceListenerUseCase =
        SetupChangeFuelPriceListenerUseCase(
            dispatcher,
            getDashboardFuelPumpsUseCase,
            changeFuelPriceRequestUseCase
        )

    @Provides
    @Singleton
    fun providesSetupFDCLogOnUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
        logInUseCase: LogInUseCase,
        startFDCUseCase: StartFDCUseCase,
        versionInfoUseCase: GetVersionInfoUseCase,
        getPumpsUseCase: GetDashboardFuelPumpsUseCase,
        setUpHeartBeat: FDCHeartBeatInitializeUseCase,
        setUpSaleTransactions: SetupFuelSaleTransactionsListenerUseCase,
        listenForFDCExceptionUseCase: ListenForFDCExceptionUseCase,
        setupFPStateChangeListener: SetupFPStateChangeListenerUseCase,
        setupChangeFuelPriceListenerUseCase: SetupChangeFuelPriceListenerUseCase
    ): SetupFDCLogOnUseCase = SetupFDCLogOnUseCase(
        dispatcher,
        logInUseCase,
        startFDCUseCase,
        versionInfoUseCase,
        getPumpsUseCase,
        setUpHeartBeat,
        setUpSaleTransactions,
        listenForFDCExceptionUseCase,
        setupFPStateChangeListener,
        setupChangeFuelPriceListenerUseCase
    )

    @Provides
    @Singleton
    fun providesValidateFuelPriceUseCase(
        @IODispatcher dispatcher: CoroutineDispatcher,
    ): ValidateFuelPriceUseCase = ValidateFuelPriceUseCase(dispatcher)
}