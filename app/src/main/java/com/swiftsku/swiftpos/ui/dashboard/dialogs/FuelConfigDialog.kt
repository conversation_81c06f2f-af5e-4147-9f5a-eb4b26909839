package com.swiftsku.swiftpos.ui.dashboard.dialogs

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TopAppBar
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Sync
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.main.state.DispenserState
import com.swiftsku.swiftpos.ui.dashboard.main.state.HeartbeatStatus
import com.swiftsku.swiftpos.ui.dashboard.main.state.ProductState
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel
import com.swiftsku.swiftpos.ui.theme.Orange
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal
import com.swiftsku.swiftpos.ui.theme.TextTitle


@Composable
fun FuelConfigDialog(
    dashboardVM: DashboardViewModel,
    fdcVM: FDCViewModel,
    onDismissRequest: () -> Unit
) {
    val storeConfig by dashboardVM.storeConfig.collectAsState()
    val fdcState by fdcVM.fdcState.collectAsState()

    Dialog(
        properties = DialogProperties(usePlatformDefaultWidth = false),
        onDismissRequest = onDismissRequest,
    ) {
        Surface(
            modifier = Modifier
                .width(500.dp)
                .heightIn(max = 600.dp),
            shape = RoundedCornerShape(8.dp)
        ) {
            Column {
                TopAppBar(
                    elevation = 4.dp,
                    title = { Text("Fuel Configuration") },
                    actions = {
                        IconButton(onClick = onDismissRequest) {
                            Icon(Icons.Filled.Clear, contentDescription = "Close")
                        }
                    }
                )

                LazyColumn(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Connection Information Section
                    item {
                        ConnectionInfoSection(
                            fuelConfig = storeConfig?.fuelConfig,
                            heartbeatStatus = fdcState.heartbeatStatus
                        )
                    }

                    // Products and Pricing Section
                    item {
                        ProductsPricingSection(
                            dispensers = fdcState.dispensers,
                            onRefreshClick = { fdcVM.refreshFuelPumpsData() }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ConnectionInfoSection(
    fuelConfig: com.swiftsku.swiftpos.data.model.FuelConfig?,
    heartbeatStatus: HeartbeatStatus
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = "Connection Information",
            style = MaterialTheme.typography.h6,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Divider(modifier = Modifier.padding(bottom = 8.dp))

        Column(modifier = Modifier.padding(start = 8.dp, end = 8.dp)) {
            // IP Address Information
            val boardIp = (fuelConfig?.boardIp ?: "Not configured").ifEmpty { "-" }
            InfoRow(label = "Board IP Address:", value = boardIp)

            val cloudIp = (fuelConfig?.boardIp ?: "Not configured").ifEmpty { "-" }
            InfoRow(label = "Cloud Pilot IP Address:", value = cloudIp)

            // Communication Priority
            InfoRow(
                label = "Communication Priority:",
                value = fuelConfig?.commPriority ?: "NOT SET"
            )

            // Current Connection Status
            val currentIp =
                when (fuelConfig?.commPriority) {
                    "board" -> fuelConfig.boardIp
                    "cloud" -> fuelConfig.cloudPilotIp
                    else -> "Unknown"
                }

            InfoRow(label = "Currently Connected To:", value = currentIp ?: "Not connected")

            // Heartbeat Status
            val statusColor =
                when (heartbeatStatus) {
                    HeartbeatStatus.RECEIVED -> Teal
                    HeartbeatStatus.SENT -> Orange
                    HeartbeatStatus.STOPPED -> Red
                    HeartbeatStatus.INITIAL -> Color.Black
                }

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Heartbeat Status:",
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp,
                    color = TextTitle
                )
                Text(
                    text = heartbeatStatus.name,
                    color = statusColor,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
private fun ProductsPricingSection(dispensers: List<DispenserState>, onRefreshClick: () -> Unit) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Fuel Products & Pricing",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            IconButton(onClick = onRefreshClick) {
                Icon(Icons.Filled.Sync, contentDescription = "Refresh fuel data", tint = Teal)
            }
        }

        Divider(modifier = Modifier.padding(bottom = 8.dp))

        if (dispensers.isEmpty()) {
            Text(
                text = "No fuel products configured",
                style = MaterialTheme.typography.body2,
                color = Color.Gray,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
        } else {
            // Get all unique products from all dispensers
            val allProducts = dispensers.flatMap { it.product }.distinctBy { it.productNo }

            allProducts.forEach { product ->
                ProductCard(product = product)
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
private fun ProductCard(product: ProductState) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
    ) {
        Text(
            text = "${product.productName} (Product #${product.productNo})",
            fontWeight = FontWeight.Bold,
            fontSize = 16.sp,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        // Pricing for different fuel modes
        val cashPrice = product.fuelPrice.find { it.modeNo == 1 }
        val cardPrice = product.fuelPrice.find { it.modeNo == 2 }

        Column(modifier = Modifier.fillMaxWidth()) {
            Row {
                Text(
                    text = "Cash Price (Mode 1): ",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = cashPrice?.price?.toFloat()?.toDollars() ?: "Not set",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Teal
                )
            }

            Row {
                Text(
                    text = "Card Price (Mode 2):",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = cardPrice?.price?.toFloat()?.toDollars() ?: "Not set",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Teal
                )
            }
        }
    }
}

@Composable
private fun InfoRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            color = TextTitle,
            fontWeight = FontWeight.Medium,
            fontSize = 14.sp,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            fontSize = 14.sp,
            color = Color.Black,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.weight(1f),
            textAlign = TextAlign.End
        )
    }
}
