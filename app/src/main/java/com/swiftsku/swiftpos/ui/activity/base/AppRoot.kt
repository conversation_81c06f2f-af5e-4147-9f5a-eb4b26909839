package com.swiftsku.swiftpos.ui.activity.base

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.swiftsku.fdc.core.di.manager.WebSocketManager
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.ui.dashboard.DashboardViewModel
import com.swiftsku.swiftpos.ui.dashboard.credits.CreditsViewModel
import com.swiftsku.swiftpos.ui.dashboard.recall.RecallViewModel
import com.swiftsku.swiftpos.ui.dashboard.saletransaction.TxnHistoryDetailViewModel
import com.swiftsku.swiftpos.ui.dashboard.txnhistory.TxnHistoryViewModel
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel
import com.swiftsku.swiftpos.ui.login.LoginViewModel
import com.swiftsku.swiftpos.ui.navigation.SwiftPosNavGraph
import com.swiftsku.swiftpos.ui.presentation.CustomerPresentation
import com.swiftsku.swiftpos.ui.report.ReportViewModel
import com.swiftsku.swiftpos.ui.theme.SwiftPOSTheme
import com.swiftsku.swiftpos.utils.buildFdcWebSocketUrl
import java.io.File

@Composable
fun AppRoot(
    configVM: ConfigViewModel,
    dashboardVM: DashboardViewModel,
    loginVM: LoginViewModel,
    fdcVM: FDCViewModel,
    recallVM: RecallViewModel,
    transactionHistoryVM: TxnHistoryViewModel,
    transactionHistoryDetailVM: TxnHistoryDetailViewModel,
    reportVM: ReportViewModel,
    creditsVM: CreditsViewModel,
    secondaryDisplay: CustomerPresentation?,
    fdcSocketManager: WebSocketManager,
    idleImageResources: File?
) {
    val appState by configVM.appState.collectAsState()
    val fqmState by fdcVM.fqmState.collectAsState()
    val fdcState by fdcVM.fdcState.collectAsState()
    var currentWebSocketUrl by remember { mutableStateOf<String?>(null) }

    if (secondaryDisplay != null) {
        val phoneNumber by secondaryDisplay.phoneNumber.collectAsState()
        LaunchedEffect(phoneNumber) {
            phoneNumber?.let {
                dashboardVM.updateLoyaltyAccountId(it.number)
                secondaryDisplay.dismissUserInput()
            }
        }
    }

    LaunchedEffect(appState, fqmState, fdcState) {
        dashboardVM.updateInfo(appState, fqmState, fdcState)
    }

    // Note: using appState.storeConfig to change the visibility of consumer screen didn't work.
    // It seems like it needs to be called multiple times to be able to show UI on that screen.
    LaunchedEffect(appState) {
        appState.storeConfig?.let { storeConfig ->
            secondaryDisplay?.showDisplay(storeConfig.displayConsumerScreen)
        }
    }

    LaunchedEffect(appState.storeConfig) {
        appState.storeConfig?.let { storeConfig ->
            loginVM.updateStoreConfig(storeConfig)
            dashboardVM.updateStoreConfig(storeConfig)
            fdcVM.updateStoreConfig(storeConfig)
            recallVM.updateStoreConfig(storeConfig)
            transactionHistoryDetailVM.updateStoreConfig(storeConfig)
            reportVM.updateStoreConfig(storeConfig)
            creditsVM.updateTerminalConfig(storeConfig)
            if (storeConfig.fuelConfig?.enabled.isTrue()) {
                val url = if (storeConfig.fuelConfig?.commPriority == "board") {
                    buildFdcWebSocketUrl(storeConfig.fuelConfig.boardIp)
                } else {
                    buildFdcWebSocketUrl(storeConfig.fuelConfig?.cloudPilotIp)
                }
                // Only reconnect if URL actually changed
                if (url != currentWebSocketUrl) {
                    currentWebSocketUrl?.let { fdcSocketManager.disconnect() }
                    url?.let {
                        currentWebSocketUrl = it
                        fdcSocketManager.connect(
                            url = it,
                            shouldLog = storeConfig.fuelConfig?.shouldLogMessages == true
                        )
                    }
                }
            } else {
                currentWebSocketUrl?.let {
                    fdcSocketManager.disconnect()
                    currentWebSocketUrl = null
                }
            }
        }
    }

    LaunchedEffect(Unit) {
        dashboardVM.setIdleImageResource(idleImageResources)
    }

    // start the fdc socket connection
    LaunchedEffect(appState.storeLevelConfig) {
        appState.storeLevelConfig?.let { storeLevelConfig ->
            transactionHistoryDetailVM.updateStoreLevelConfig(storeLevelConfig)
            dashboardVM.updateStoreLevelConfig(storeLevelConfig)
            creditsVM.updateStoreConfig(storeLevelConfig)
            fdcVM.updateStoreLevelConfig(storeLevelConfig)
        }
    }


    SwiftPOSTheme {
        SwiftPosNavGraph(
            secondaryDisplay = secondaryDisplay,
            appState = appState,
            dashboardVM = dashboardVM,
            loginVM = loginVM,
            fdcVM = fdcVM,
            recallVM = recallVM,
            transactionHistoryDetailVM = transactionHistoryDetailVM,
            transactionHistoryVM = transactionHistoryVM,
            reportVM = reportVM,
            creditsVM = creditsVM
        )
    }
}