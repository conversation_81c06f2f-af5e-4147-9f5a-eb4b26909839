package com.swiftsku.swiftpos.ui.dashboard

import android.os.CountDownTimer
import androidx.compose.ui.input.key.KeyEvent
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.apollographql.apollo3.api.Optional
import com.fdc.core.types.FQMMessage
import com.pax.poscore.commsetting.TcpSetting
import com.pax.poscore.commsetting.UsbSetting
import com.pax.poslinkadmin.constant.FileType
import com.pax.poslinkadmin.constant.InputTextPrompt
import com.pax.poslinkadmin.constant.InputType
import com.pax.poslinkadmin.form.InputTextRequest
import com.pax.poslinkadmin.manage.UpdateResourceFileRequest
import com.pax.poslinksemiintegration.constant.EbtCountType
import com.swiftsku.swiftpos.data.couchbase.analytics.EventLogger
import com.swiftsku.swiftpos.data.couchbase.config.StoreRepository
import com.swiftsku.swiftpos.data.couchbase.event.EventRepository
import com.swiftsku.swiftpos.data.couchbase.gupc.GUPCRepository
import com.swiftsku.swiftpos.data.couchbase.gupc.correction.GUPCCorrectionRepository
import com.swiftsku.swiftpos.data.couchbase.pricebook.PriceBookRepository
import com.swiftsku.swiftpos.data.couchbase.refdata.RefDataRepository
import com.swiftsku.swiftpos.data.couchbase.transaction.TransactionRepository
import com.swiftsku.swiftpos.data.couchbase.user.UserRepository
import com.swiftsku.swiftpos.data.local.datastore.notification.NotificationsDataSource
import com.swiftsku.swiftpos.data.local.datastore.user.UserDataSource
import com.swiftsku.swiftpos.data.local.datastore.user.dto.UserDTO
import com.swiftsku.swiftpos.data.local.datastore.user.dto.isOwner
import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.AvailableOffer
import com.swiftsku.swiftpos.data.model.BaseTransaction
import com.swiftsku.swiftpos.data.model.CardInfo
import com.swiftsku.swiftpos.data.model.CardPayment
import com.swiftsku.swiftpos.data.model.CashDepositData
import com.swiftsku.swiftpos.data.model.CashDepositTxn
import com.swiftsku.swiftpos.data.model.CashPayment
import com.swiftsku.swiftpos.data.model.CashWithdrawalData
import com.swiftsku.swiftpos.data.model.CashWithdrawalTxn
import com.swiftsku.swiftpos.data.model.ChequePayment
import com.swiftsku.swiftpos.data.model.Coupon
import com.swiftsku.swiftpos.data.model.CreditAccount
import com.swiftsku.swiftpos.data.model.CreditPayment
import com.swiftsku.swiftpos.data.model.Department
import com.swiftsku.swiftpos.data.model.EBTPayment
import com.swiftsku.swiftpos.data.model.EbtInfo
import com.swiftsku.swiftpos.data.model.EbtType
import com.swiftsku.swiftpos.data.model.EpxData
import com.swiftsku.swiftpos.data.model.Event
import com.swiftsku.swiftpos.data.model.FuelMeta
import com.swiftsku.swiftpos.data.model.Info
import com.swiftsku.swiftpos.data.model.ItemDiscount
import com.swiftsku.swiftpos.data.model.LinkedPluData
import com.swiftsku.swiftpos.data.model.LogOutEventData
import com.swiftsku.swiftpos.data.model.LotteryPayout
import com.swiftsku.swiftpos.data.model.LoyaltyAccountInfo
import com.swiftsku.swiftpos.data.model.LoyaltyInput
import com.swiftsku.swiftpos.data.model.LoyaltyState
import com.swiftsku.swiftpos.data.model.MenuKey
import com.swiftsku.swiftpos.data.model.NoSaleTransaction
import com.swiftsku.swiftpos.data.model.Payout
import com.swiftsku.swiftpos.data.model.PayoutEvent
import com.swiftsku.swiftpos.data.model.PinPadConnectionType
import com.swiftsku.swiftpos.data.model.PinpadConfig
import com.swiftsku.swiftpos.data.model.PluItem
import com.swiftsku.swiftpos.data.model.PricebookSearchItem
import com.swiftsku.swiftpos.data.model.PromotionsData
import com.swiftsku.swiftpos.data.model.PumpMeta
import com.swiftsku.swiftpos.data.model.RefundTransaction
import com.swiftsku.swiftpos.data.model.ReleaseData
import com.swiftsku.swiftpos.data.model.ReleaseUpdateType
import com.swiftsku.swiftpos.data.model.ReportMissingItem
import com.swiftsku.swiftpos.data.model.ReportWrongItem
import com.swiftsku.swiftpos.data.model.SaleTransaction
import com.swiftsku.swiftpos.data.model.StoreConfig
import com.swiftsku.swiftpos.data.model.StoreUsers
import com.swiftsku.swiftpos.data.model.Tax
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.data.model.Transaction
import com.swiftsku.swiftpos.data.model.TransactionItem
import com.swiftsku.swiftpos.data.model.TransactionItemWithDepartment
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.TransactionItemWithPLUItem
import com.swiftsku.swiftpos.data.model.TxnDiscount
import com.swiftsku.swiftpos.data.model.TxnPayment
import com.swiftsku.swiftpos.data.model.UID
import com.swiftsku.swiftpos.data.model.UserSettings
import com.swiftsku.swiftpos.data.model.VoidTransaction
import com.swiftsku.swiftpos.data.model.applyEBT
import com.swiftsku.swiftpos.data.model.convertToRefundTransaction
import com.swiftsku.swiftpos.data.model.convertToSaleTransaction
import com.swiftsku.swiftpos.data.model.generateTransactionId
import com.swiftsku.swiftpos.data.model.hasCardPayment
import com.swiftsku.swiftpos.data.model.hasFuel
import com.swiftsku.swiftpos.data.model.hasPostFuel
import com.swiftsku.swiftpos.data.model.isEBT
import com.swiftsku.swiftpos.data.model.isEpxCaptured
import com.swiftsku.swiftpos.data.model.isUnderAge
import com.swiftsku.swiftpos.data.model.isValidTransactionId
import com.swiftsku.swiftpos.data.model.itemName
import com.swiftsku.swiftpos.data.model.totalItemPrice
import com.swiftsku.swiftpos.data.provider.DashboardStateProvider
import com.swiftsku.swiftpos.data.remote.repositories.loyalty.LoyaltyRepository
import com.swiftsku.swiftpos.data.remote.repositories.payment.PaymentRepository
import com.swiftsku.swiftpos.data.type.EventType
import com.swiftsku.swiftpos.data.type.LoyaltyAccountStatus
import com.swiftsku.swiftpos.data.type.NumPadResult
import com.swiftsku.swiftpos.data.type.PayoutType
import com.swiftsku.swiftpos.data.type.PluFormatType
import com.swiftsku.swiftpos.data.type.StatusReason
import com.swiftsku.swiftpos.data.type.ToastMessageType
import com.swiftsku.swiftpos.data.type.TransactionItemStatus
import com.swiftsku.swiftpos.data.type.TransactionStatus
import com.swiftsku.swiftpos.data.type.TransactionType
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.di.qualifiers.FirebaseEventLoggerQualifier
import com.swiftsku.swiftpos.di.qualifiers.IODispatcher
import com.swiftsku.swiftpos.di.qualifiers.PrinterX
import com.swiftsku.swiftpos.di.qualifiers.QualifierCouchbaseConfigRepository
import com.swiftsku.swiftpos.di.qualifiers.UserRepositoryCache
import com.swiftsku.swiftpos.domain.cart.DeleteCartItemUseCase
import com.swiftsku.swiftpos.domain.cart.dto.DeleteCartItemInput
import com.swiftsku.swiftpos.domain.config.SavePLUItemsOrderUseCase
import com.swiftsku.swiftpos.domain.config.UpdateEPXTerminalIPUseCase
import com.swiftsku.swiftpos.domain.departments.UpdateDepartmentTxnItemsUseCase
import com.swiftsku.swiftpos.domain.departments.dto.DepartmentUpdateQuantity
import com.swiftsku.swiftpos.domain.departments.dto.RecalculateAmount
import com.swiftsku.swiftpos.domain.dispenser.PendingFuelTxnUseCase
import com.swiftsku.swiftpos.domain.dispenser.ValidateFuelPriceUseCase
import com.swiftsku.swiftpos.domain.info.GetInfoUseCase
import com.swiftsku.swiftpos.domain.payment.PaymentUseCase
import com.swiftsku.swiftpos.domain.payment.dto.DayCloseInputDto
import com.swiftsku.swiftpos.domain.payment.dto.PaxPaymentInput
import com.swiftsku.swiftpos.domain.payout.CreateLotteryPayoutUseCase
import com.swiftsku.swiftpos.domain.payout.dto.PayoutInput
import com.swiftsku.swiftpos.domain.pricebook.UpdatePriceBookTxnItemsUseCase
import com.swiftsku.swiftpos.domain.pricebook.dto.JustUpdateItemLines
import com.swiftsku.swiftpos.domain.pricebook.dto.PriceBookAdd
import com.swiftsku.swiftpos.domain.pricebook.dto.PriceBookIncrement
import com.swiftsku.swiftpos.domain.pricebook.dto.PriceBookUpdateQuantity
import com.swiftsku.swiftpos.domain.printer.PrintEODReportUseCase
import com.swiftsku.swiftpos.domain.printer.PrintTransactionUseCase
import com.swiftsku.swiftpos.domain.printer.dto.PrintBalanceInput
import com.swiftsku.swiftpos.domain.printer.dto.PrintTxnReceiptInput
import com.swiftsku.swiftpos.domain.promotion.GetPromotionUserCase
import com.swiftsku.swiftpos.domain.promotion.dto.AppliedPromotion
import com.swiftsku.swiftpos.domain.refdata.RefDataUseCase
import com.swiftsku.swiftpos.domain.report.GetShiftReportUseCase
import com.swiftsku.swiftpos.domain.support.SupportUseCase
import com.swiftsku.swiftpos.domain.tax.GetTaxForAmountMapUseCase
import com.swiftsku.swiftpos.domain.tax.GetTaxMap
import com.swiftsku.swiftpos.domain.transaction.CompleteTransactionUseCase
import com.swiftsku.swiftpos.domain.transaction.GetFeesUseCase
import com.swiftsku.swiftpos.domain.transaction.GetTransactionSummaryUseCase
import com.swiftsku.swiftpos.domain.transaction.UpdateTransactionCollectedAmountUseCase
import com.swiftsku.swiftpos.domain.transaction.dto.CompleteTxnInput
import com.swiftsku.swiftpos.extension.combineFlow
import com.swiftsku.swiftpos.extension.convertDollarToCentPrecisely
import com.swiftsku.swiftpos.extension.convertToEAN
import com.swiftsku.swiftpos.extension.epochInSeconds
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.extension.openAsync
import com.swiftsku.swiftpos.extension.sumOfFloats
import com.swiftsku.swiftpos.extension.toDate
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.modules.hotp.HOTPGenerator
import com.swiftsku.swiftpos.modules.printer.EODReportTemplate
import com.swiftsku.swiftpos.modules.printer.ISunMiPrinter
import com.swiftsku.swiftpos.modules.serialkey.DeviceIdentifier
import com.swiftsku.swiftpos.modules.websocket.payment.PaymentInputDTO
import com.swiftsku.swiftpos.modules.websocket.payment.PaymentProcessor
import com.swiftsku.swiftpos.modules.websocket.payment.PaymentResponseWS
import com.swiftsku.swiftpos.modules.websocket.payment.PaymentType
import com.swiftsku.swiftpos.modules.websocket.payment.TData
import com.swiftsku.swiftpos.modules.websocket.payment.TxnType
import com.swiftsku.swiftpos.payment.type.TIM
import com.swiftsku.swiftpos.services.payment.pax.PaxConnectionResult
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.ui.activity.base.BaseAppState
import com.swiftsku.swiftpos.ui.dashboard.dialogs.FundOption
import com.swiftsku.swiftpos.ui.dashboard.main.state.DashboardState
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import com.swiftsku.swiftpos.ui.dashboard.main.state.FuelPumpState
import com.swiftsku.swiftpos.ui.dashboard.main.state.GUPCItem
import com.swiftsku.swiftpos.ui.dashboard.main.state.PaymentState
import com.swiftsku.swiftpos.ui.dashboard.main.state.PayoutState
import com.swiftsku.swiftpos.ui.dashboard.main.state.TransactionSummary
import com.swiftsku.swiftpos.ui.dashboard.main.state.allowAddingItemsToCart
import com.swiftsku.swiftpos.ui.dashboard.main.state.change
import com.swiftsku.swiftpos.ui.dashboard.main.state.disableEBT
import com.swiftsku.swiftpos.ui.dashboard.main.state.hasDefault
import com.swiftsku.swiftpos.ui.dashboard.main.state.isCompleted
import com.swiftsku.swiftpos.ui.dashboard.main.state.pendingAmount
import com.swiftsku.swiftpos.ui.dashboard.main.state.totalAmountCollected
import com.swiftsku.swiftpos.ui.dashboard.main.state.totalEBT
import com.swiftsku.swiftpos.ui.dashboard.saletransaction.RefundState
import com.swiftsku.swiftpos.ui.navigation.Routes
import com.swiftsku.swiftpos.utils.ENABLE_EMULATOR
import com.swiftsku.swiftpos.utils.EventUtils
import com.swiftsku.swiftpos.utils.MAX_ATTEMPTS_REACHED
import com.swiftsku.swiftpos.utils.Result
import com.swiftsku.swiftpos.utils.isDlInput
import com.swiftsku.swiftpos.utils.parseDobFromPdf417
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.yield
import java.io.File
import java.util.Calendar
import java.util.Date
import java.util.TimeZone
import javax.inject.Inject
import com.pax.poslinkadmin.constant.TransactionType as EpxTransactionType

@HiltViewModel
class DashboardViewModel
@Inject
constructor(
    private val savedStateHandle: SavedStateHandle,
    private val priceBookRepository: PriceBookRepository,
    private val gUPCRepository: GUPCRepository,
    private val refDataRepository: RefDataRepository,
    private val transactionRepository: TransactionRepository,
    private val paymentRepository: PaymentRepository,
    private val loyaltyRepository: LoyaltyRepository,
    @QualifierCouchbaseConfigRepository private val storeRepository: StoreRepository,
    @PrinterX private val printerXManager: ISunMiPrinter,
    private val eventRepository: EventRepository,
    private val gUPCCorrectionRepository: GUPCCorrectionRepository,
    @IODispatcher private val ioDispatcher: CoroutineDispatcher,
    @FirebaseEventLoggerQualifier private val eventLogger: EventLogger,
    private val getTaxForAmountMapUseCase: GetTaxForAmountMapUseCase,
    private val getTransactionSummaryUseCase: GetTransactionSummaryUseCase,
    private val updatePriceBookTxnItemsUseCase: UpdatePriceBookTxnItemsUseCase,
    private val updateDepartmentTxnItemsUseCase: UpdateDepartmentTxnItemsUseCase,
    private val printEODReportUseCase: PrintEODReportUseCase,
    private val deleteCartItemUseCase: DeleteCartItemUseCase,
    private val infoUseCase: GetInfoUseCase,
    private val userDataSource: UserDataSource,
    private val notificationsDataSource: NotificationsDataSource,
    private val getShiftReportUseCase: GetShiftReportUseCase,
    private val completeTransaction: CompleteTransactionUseCase,
    private val hOtpGenerator: HOTPGenerator,
    private val refDataUseCase: RefDataUseCase,
    private val printTransactionUseCase: PrintTransactionUseCase,
    @UserRepositoryCache private val userRepository: UserRepository,
    val paymentProcessor: PaymentProcessor,
    private val dashboardStateProvider: DashboardStateProvider,
    private val paxPaymentService: PaxPaymentService,
    private val paymentUseCase: PaymentUseCase,
    private val updateTransactionCollectedAmount: UpdateTransactionCollectedAmountUseCase,
    private val pendingFuelTxnUseCase: PendingFuelTxnUseCase,
    private val createLotteryPayout: CreateLotteryPayoutUseCase,
    private val validateFuelPriceUseCase: ValidateFuelPriceUseCase,
    private val getPromotionUseCase: GetPromotionUserCase,
    private val savePLUItemsOrder: SavePLUItemsOrderUseCase,
    private val updateEPXTerminalIP: UpdateEPXTerminalIPUseCase,
    private val supportUseCase: SupportUseCase
) : ViewModel() {

    private var barcode = ""
    private var drivingLicenceData = ""
    private val timer: CountDownTimer =
        object : CountDownTimer(Long.MAX_VALUE, 5000) {
            override fun onTick(p0: Long) {
                val previousTimer = _today.value
                val currentTime =
                    Calendar.getInstance(TimeZone.getDefault())
                        .also {
                            it.set(Calendar.HOUR_OF_DAY, 0)
                            it.set(Calendar.MINUTE, 0)
                            it.set(Calendar.SECOND, 0)
                            it.set(Calendar.MILLISECOND, 0)
                        }
                        .timeInMillis

                if (currentTime > previousTimer) {
                    updateCustomerCount(currentTime)
                }
            }

            override fun onFinish() {
                this.start()
            }
        }
            .start()

    val currentUser: StateFlow<UserDTO?> = userDataSource.currentUserFlow

    private val _storeConfig = MutableStateFlow<StoreConfig?>(null)
    private val _storeLevelConfig = MutableStateFlow<StoreUsers?>(null)
    val storeLevelConfig: StateFlow<StoreUsers?> = _storeLevelConfig
    val storeConfig = _storeConfig
    private val _cardInfo = MutableStateFlow<CardInfo?>(null)
    private var ebtInfo: EbtInfo? = null

    private val _userSettings = MutableStateFlow<UserSettings?>(null)
    val userSettings: StateFlow<UserSettings?> = _userSettings

    private val _currentCouponCount = MutableStateFlow(1)
    private val _selectedPLUItem = MutableStateFlow<PluItem?>(null)
    val selectedPLUItem = _selectedPLUItem
    private val _selectedDepartment = MutableStateFlow<Department?>(null)
    val selectedDepartment = _selectedDepartment
    private val _isLoading = MutableStateFlow<String?>(null)

    val txnItems = dashboardStateProvider.txnItems
    private val _customerAge = MutableStateFlow<String?>(null)
    private val _searchQuery = MutableStateFlow("")
    private val _enableSkip = MutableStateFlow(false)
    private val _selectedTransactionItem = MutableStateFlow<TransactionItem?>(null)
    val selectedTransactionItem = _selectedTransactionItem
    private val _cashDrawerOpenTime = MutableStateFlow<Date?>(null)
    private val _today =
        MutableStateFlow(
            Calendar.getInstance(TimeZone.getDefault())
                .also {
                    it.set(Calendar.HOUR_OF_DAY, 0)
                    it.set(Calendar.MINUTE, 0)
                    it.set(Calendar.SECOND, 0)
                    it.set(Calendar.MILLISECOND, 0)
                }
                .timeInMillis
        )

    data class SavedFuelTxn(val txn: Transaction, val txnPaymentType: TxnPaymentType)

    private val _savedFuelTxn = MutableStateFlow<SavedFuelTxn?>(null)
    val savedFuelTxn = _savedFuelTxn

    private val _logOutEvent = MutableStateFlow(false)
    val logOutEvent = _logOutEvent

    private val _toastMessage = MutableStateFlow<ToastMessage?>(null)
    val toastMessage = _toastMessage

    private val _unreadNotes = MutableStateFlow<List<String>>(emptyList())
    val unreadNotes: StateFlow<List<String>> = _unreadNotes
    private val _releaseNotes = MutableStateFlow<List<ReleaseData>>(emptyList())
    val releaseNotes: StateFlow<List<ReleaseData>> = _releaseNotes

    private val _uiState = MutableStateFlow(DashboardState())
    private val _currentTransaction = MutableStateFlow<Transaction?>(null)
    val currentTransaction: StateFlow<Transaction?> = _currentTransaction

    private val _logoutCashBalance = MutableStateFlow(0f)

    private val _txnPayment =
        MutableStateFlow<MutableMap<TxnPaymentType, TxnPayment>>(mutableMapOf())

    private val _customerCountAndCurrentSales: MutableStateFlow<Pair<Int, Float>> =
        MutableStateFlow(Pair(0, 0f))

    private val _info = MutableStateFlow(Info.empty())
    val info = _info

    private val taxMap = mutableMapOf<String, Tax>()
    private val _taxList: MutableStateFlow<List<Tax>> = MutableStateFlow(emptyList())

    private val _priceBookList: StateFlow<List<PluItem>> =
        priceBookRepository
            .getPriceBookOnDashboardStream()
            .catch { EventUtils.recordException(it) }
            .stateIn(
                viewModelScope,
                SharingStarted.Eagerly,
                _uiState.value.priceBookList
            )

    private val _lotteryNames: MutableStateFlow<List<String>> = MutableStateFlow(emptyList())

    private val _vendorNames: MutableStateFlow<List<String>> = MutableStateFlow(emptyList())

    var departments = listOf<Department>()
    private val _departmentList = MutableStateFlow(_uiState.value.departmentList)

    private val _paxTerminalState = MutableStateFlow<PaxConnectionResult?>(null)
    val paxTerminalState: StateFlow<PaxConnectionResult?> = _paxTerminalState
    private val _menuKeys = MutableStateFlow<List<MenuKey>>(emptyList())
    val menuKeys: StateFlow<List<MenuKey>> = _menuKeys
    private val _menuItems = MutableStateFlow<List<PluItem>>(emptyList())
    val menuItems: StateFlow<List<PluItem>> = _menuItems
    private val _deptPluItems = MutableStateFlow<List<PluItem>>(emptyList())
    val deptPluItems: StateFlow<List<PluItem>> = _deptPluItems
    private val _pendingFuelTxns = MutableStateFlow<List<SaleTransaction>>(emptyList())
    val pendingFuelTxns: StateFlow<List<SaleTransaction>> = _pendingFuelTxns
    private val _loadings = MutableStateFlow<MutableSet<Loadings>>(mutableSetOf())
    val loadings: StateFlow<Set<Loadings>> = _loadings
    private val _transactionScanned =
        MutableSharedFlow<String>(replay = 0, extraBufferCapacity = 1)
    val transactionScanned: SharedFlow<String> = _transactionScanned
    private val _event = MutableStateFlow<ViewModelEvent?>(null)
    val event: StateFlow<ViewModelEvent?> = _event
    private var promotionsData: PromotionsData? = null
    private val _appliedPromotions = MutableStateFlow<List<AppliedPromotion>>(emptyList())
    private var refundTxn: RefundTransaction? = null
    private var idleResourceFile: File? = null
    private var releaseNotesJob: Job? = null
    private var pendingFuelListenerJob: Job? = null

    sealed class ViewModelEvent {
        object AskUsbPermission : ViewModelEvent()
        data class ScheduleEpxBatchClose(
            val pinpadConfig: PinpadConfig,
            val storeCode: String,
            val posNo: String,
            val posId: String,
            val cashierId: String
        ) : ViewModelEvent()

        object CancelEpxBatchClose : ViewModelEvent()
        data class RefreshFuelTransactions(val fuelPump: FuelPumpState) : ViewModelEvent()
    }

    enum class Loadings {
        MENU_ITEMS,
        EBT_BALANCE,
        DEPT_PLU_ITEMS,
        PAYMENT_PROCESS,
        SAVING_TXN,
        CLOSING_DAY,
        CLOSING_TXNS,
        REFUNDING
    }

    init {
        initConfig()
        initCurrentUser()
        updateCustomerCount(_today.value)
        initCompleteTransaction()
        addCouchbaseIndexes()
    }

    private fun addCouchbaseIndexes() {
        viewModelScope.launch(ioDispatcher) { priceBookRepository.addIndexes() }
    }

    fun updateStoreConfig(storeConfig: StoreConfig) =
        viewModelScope.launch {
            _storeConfig.emit(storeConfig)
            pendingFuelListenerJob?.cancel()
            if (storeConfig.fuelConfig?.enabled.isTrue()) {
                pendingFuelListenerJob = setupPendingFuelTxnListener()
            } else {
                _pendingFuelTxns.emit(emptyList())
            }
        }

    fun updateStoreLevelConfig(storeUsers: StoreUsers) =
        viewModelScope.launch { _storeLevelConfig.emit(storeUsers) }

    private fun initCurrentUser() =
        viewModelScope.launch {
            combine(currentUser.filterNotNull(), _storeConfig.filterNotNull()) { user,
                                                                                 config ->
                user to config
            }
                .distinctUntilChanged { old, new ->
                    old.first.username == new.first.username
                }
                .collect { (user, config) ->
                    EventUtils.setUserProperty(
                        EventUtils.UserProp.USER_NAME,
                        user.username
                    )
                    getUserSettings(config.storeCode, user.username)
                    releaseNotesJob?.cancel()
                    releaseNotesJob = getReleaseNotes(user.username)
                }
        }

    private fun initPaxTerminal(storeConfig: StoreConfig, storeLevelConfig: StoreUsers) =
        viewModelScope.launch {
            if (storeLevelConfig.payfac != "epx") {
                return@launch
            }
            storeConfig.pinpadConfig?.let {
                val commSetting =
                    when (it.ctype) {
                        PinPadConnectionType.USB -> {
                            _event.emit(ViewModelEvent.AskUsbPermission)
                            UsbSetting()
                        }

                        PinPadConnectionType.TCP ->
                            TcpSetting(
                                it.ip,
                                it.port
                                    ?: PaxPaymentService
                                        .DEFAULT_PORT,
                                PaxPaymentService.DEFAULT_TIMEOUT
                            )
                    }

                paxPaymentService.connectToDevice(commSetting).collect { paxState ->
                    _paxTerminalState.emit(paxState)
                    _info.update { info ->
                        info.copy(paxTerminalState = paxState)
                    }
                    if (paxState is PaxConnectionResult.ConnectionError) {
                        showToast(
                            "Failed to connect to PAX terminal. ${paxState.message}",
                            ToastMessageType.Error
                        )
                    }
                    if (paxState is PaxConnectionResult.ConnectionSuccess) {
                        it.batchCloseEnabled?.let { enableBatchClose ->
                            if (enableBatchClose) {
                                _event.emit(
                                    ViewModelEvent
                                        .ScheduleEpxBatchClose(
                                            it,
                                            storeConfig
                                                .storeCode,
                                            storeConfig
                                                .posNumber,
                                            storeConfig
                                                .posId,
                                            cashierId()
                                        )
                                )
                            } else {
                                _event.emit(
                                    ViewModelEvent
                                        .CancelEpxBatchClose
                                )
                            }
                        }
                        setPinpadIdleImage()
                    }
                }
            }
        }

    private fun initCompleteTransaction() =
        viewModelScope.launch {
            _uiState.collectLatest { state ->
                val cardPaymentState = state.cardPaymentState
                val cashPaymentState = state.cashPaymentState
                val ebtPaymentState = state.ebtPaymentState
                val checkPaymentState = state.checkPaymentState
                val creditPaymentState = state.creditPaymentState
                val storeConfig = _storeConfig.value ?: return@collectLatest

                if (cardPaymentState.hasDefault() &&
                    cashPaymentState.hasDefault() &&
                    checkPaymentState.hasDefault() &&
                    creditPaymentState.hasDefault()
                ) {
                    return@collectLatest
                }

                if (cardPaymentState.isCompleted() &&
                    cashPaymentState.isCompleted() &&
                    ebtPaymentState.isCompleted() &&
                    checkPaymentState.isCompleted() &&
                    creditPaymentState.isCompleted()
                ) {

                    val transactionSummary = state.transactionSummary
                    val txnPayment = _txnPayment.value
                    val loyaltyId = state.loyaltyState.loyaltyId
                    val cardInfo = _cardInfo.value
                    val dob = _customerAge.value
                    val txnItems = txnItems.value
                    val paymentRecord = _currentTransaction.value?.paymentRecord

                    val coupons = state.coupons
                    val transaction = transaction()
                    val hasFuel = transaction.hasFuel

                    val result =
                        completeTransaction.invoke(
                            CompleteTxnInput(
                                transaction = transaction,
                                txnPayment = txnPayment,
                                cardInfo = cardInfo,
                                ebtInfo = ebtInfo,
                                transactionSummary =
                                transactionSummary,
                                payout = state.payout,
                                loyaltyId = loyaltyId,
                                dob = dob,
                                txnItems = txnItems,
                                coupons = coupons,
                                storeConfig = storeConfig,
                                cashierId = transaction.cashierId,
                                hasFuel = hasFuel,
                                hasPostFuel =
                                transaction.hasPostFuel,
                                paymentRecord = paymentRecord,
                                printTxnIdBarcode =
                                _storeConfig
                                    .value
                                    ?.printTxnIdBarcode,
                                appliedFees = state.appliedFees
                            )
                        )

                    if (result.txnSaved) {
                        if (hasFuel) {
                            /**
                             * In case of multiple MOP, we use cash
                             * price for fuel.
                             */
                            val paymentType =
                                when {
                                    txnPayment.contains(
                                        TxnPaymentType.Card
                                    ) &&
                                            txnPayment.contains(
                                                TxnPaymentType
                                                    .Cash
                                            ) ->
                                        TxnPaymentType.Cash

                                    txnPayment.contains(
                                        TxnPaymentType.Card
                                    ) -> TxnPaymentType.Card

                                    else -> TxnPaymentType.Cash
                                }
                            _savedFuelTxn.emit(
                                SavedFuelTxn(
                                    transaction,
                                    paymentType
                                )
                            )
                        }
                        resetTransactionSwapState(null)
                    }
                }
            }
        }

    private fun initConfig() =
        viewModelScope.launch {
            combine(_storeConfig, _storeLevelConfig) { sConfig, tConfig ->
                sConfig to tConfig
            }
                .collectLatest {
                    val storeConfig = it.first
                    val storeLevelConfig = it.second
                    if (storeConfig != null && storeLevelConfig != null) {
                        initRefState(storeConfig)
                        val storeCode = storeConfig.storeCode
                        getDepartments(storeCode)
                        streamMenuKeys(storeCode)
                        getTaxes(storeCode)
                        paxPaymentService.initCurrentBatch(
                            storeCode,
                            storeConfig.posNumber,
                            storeConfig.posId
                        )
                        EventUtils.setUserProperty(
                            EventUtils.UserProp.POS_NO,
                            storeConfig.posNumber
                        )
                        EventUtils.setUserProperty(
                            EventUtils.UserProp.POS_ID,
                            storeConfig.posId
                        )
                        if (storeLevelConfig.showOffers.isTrue()) {
                            promotionsData =
                                getPromotionUseCase
                                    .getAllPromotions(storeCode)
                        }
                        initInfo(storeConfig, storeLevelConfig)
                        if (storeLevelConfig.payfac == "epx") {
                            initPaxTerminal(
                                storeConfig,
                                storeLevelConfig
                            )
                        }
                    }
                }
        }

    private fun getDepartments(storeCode: String) =
        viewModelScope.launch {
            refDataRepository.getDepartmentOnDashboardStream(storeCode).collectLatest {
                _departmentList.emit(it.toList())
            }
        }

    private fun getUserSettings(storeCode: String, userName: String) =
        viewModelScope.launch {
            userRepository.getStoreConfigStream(storeCode).collectLatest {
                it?.users?.get(userName)?.settings?.let { _userSettings.emit(it) }
                _storeLevelConfig.emit(it)
            }
        }

    private fun streamMenuKeys(storeCode: String) =
        viewModelScope.launch {
            refDataRepository.getMenuKeysStream(storeCode).collectLatest {
                // "00000000000031-000"
                _menuKeys.value = it.toList()
                streamMenuItems(storeCode)
            }
        }

    private fun streamMenuItems(storeCode: String) =
        viewModelScope.launch {
            _loadings.update { it.apply { add(Loadings.MENU_ITEMS) } }
            val menuPluItemIds = _menuKeys.value.flatMap { it.items }
            val pluIds =
                menuPluItemIds
                    .map {
                        val pluId = it.split("-")[0]
                        // "1ff6e-00000000000031"
                        "$storeCode-$pluId"
                    }
                    .toSet()
                    .toList()
            if (pluIds.isNotEmpty()) {
                priceBookRepository.getPriceBookStream(pluIds).collectLatest {
                    _menuItems.value = it.toList()
                    _loadings.update {
                        it.apply { remove(Loadings.MENU_ITEMS) }
                    }
                }
            }
        }

    private fun getReleaseNotes(userId: String) =
        viewModelScope.launch {
            combine(
                supportUseCase.getReleaseNotes(),
                notificationsDataSource.getReadNotifications(userId)
            ) { releaseDoc, readNotifications ->
                val allNotes = releaseDoc?.releases.orEmpty()
                val unread =
                    allNotes
                        .filterNot {
                            readNotifications.contains(it.version)
                        }
                        .map { it.version }
                allNotes to unread
            }
                .distinctUntilChanged()
                .collectLatest { (allNotes, unread) ->
                    _unreadNotes.emit(unread)
                    _releaseNotes.emit(allNotes)
                    val lastUpdate = allNotes.firstOrNull()
                    if (lastUpdate?.updateType == ReleaseUpdateType.MAJOR &&
                        lastUpdate.version in unread
                    ) {
                        showWhatsNew()
                    }
                }
        }

    fun addReadNotificationId(notificationId: String) =
        viewModelScope.launch {
            if (notificationId.isEmpty()) return@launch
            notificationsDataSource.addReadNotification(cashierId(), notificationId)
        }

    private fun initRefState(config: StoreConfig) =
        viewModelScope.launch {
            //        refDataRepository
            //            .getVendorNames(storeCode = config.storeCode)
            //            .collectLatest { _vendorNames.emit(it) }

            refDataRepository.getPayoutOptions(storeCode = config.storeCode)
                .collectLatest { options ->
                    _lotteryNames.emit(options.lottery)
                    _vendorNames.emit(options.vendor)
                }
        }

    private fun getTaxes(storeCode: String) =
        viewModelScope.launch {
            refDataRepository.getTaxes(storeCode).collectLatest {
                taxMap.putAll(it.associateBy { it.taxId })
                _taxList.emit(it)
            }
        }

    private fun initInfo(storeConfig: StoreConfig, storeLevelConfig: StoreUsers) =
        viewModelScope.launch {
            var info =
                infoUseCase(storeConfig = storeConfig, previousInfo = _info.value)
            storeLevelConfig.supportHours?.let { info = info.copy(operationHour = it) }
            _info.emit(info)
        }

    fun onInfoClick() = viewModelScope.launch { _uiState.update { it.copy(showInfo = true) } }

    fun onInfoTypeClick(infoType: String) {
        when (infoType) {
            "Pax Terminal" -> {
                val storeConfig = _storeConfig.value
                val storeLevelConfig = _storeLevelConfig.value
                if (storeConfig != null && storeLevelConfig != null) {
                    initPaxTerminal(storeConfig, storeLevelConfig)
                }
            }
        }
    }

    fun onInfoTitleClick(infoType: String) {
        when (infoType) {
            "Pax Terminal" -> _uiState.update { it.copy(showIpInput = true) }
            "Fuel Configs" -> _uiState.update { it.copy(showFuelConfigs = true) }
        }
    }

    fun dismissIpInputDialog() =
        viewModelScope.launch { _uiState.update { it.copy(showIpInput = false) } }

    fun dismissFuelConfigDialog() =
        viewModelScope.launch { _uiState.update { it.copy(showFuelConfigs = false) } }

    fun setEpxTerminalIp(ip: String) =
        viewModelScope.launch {
            val posId = DeviceIdentifier.getSerialNumber() ?: return@launch
            val saved = updateEPXTerminalIP(posId, ip)
            if (saved) {
                dismissIpInputDialog()
                showToast("Saved successfully", ToastMessageType.Success)
            }
        }

    private fun updateCustomerCount(date: Long) =
        viewModelScope.launch {
            _today.emit(date)
            transactionRepository
                .getCustomerCountAndCurrentSales(date)
                .catch { EventUtils.recordException(it) }
                .collectLatest { pair -> _customerCountAndCurrentSales.emit(pair) }
        }

    private fun reorderItems(items: List<PluItem>, storeConfig: StoreConfig?): List<PluItem> {
        storeConfig?.pluOrder?.let { ordering ->
            if (ordering.isNotEmpty()) {
                val itemMap = items.associateBy { it.UID() }
                val reorderedItems =
                    ordering
                        .mapNotNull { itemId -> itemMap[itemId] }
                        .toMutableList()
                // Add remaining items not in the order list to the end
                val remainingItems = items.filterNot { it.UID() in ordering }
                reorderedItems.addAll(remainingItems)
                return reorderedItems
            }
        }
        return items
    }

    fun showToast(message: String, type: ToastMessageType) =
        viewModelScope.launch {
            _toastMessage.emit(ToastMessage(message = message, type = type))
        }

    val uiState: StateFlow<DashboardState> =
        combineFlow(
            _uiState,
            _isLoading,
            txnItems,
            _departmentList,
            _priceBookList,
            _customerCountAndCurrentSales,
            _taxList,
            _vendorNames,
            _lotteryNames,
            _storeConfig
        ) { uiState,
            loading,
            txnItems,
            departmentList,
            priceBookList,
            customerCount,
            taxList,
            vendorNames,
            lotteryNames,
            storeConfig ->
            val initialState = uiState
            val cartItems =
                txnItems.filter {
                    it.status == TransactionItemStatus.Normal
                }
            val couponAmount =
                uiState.coupons
                    .takeIf { it.isNotEmpty() }
                    ?.reduce { acc, coupon ->
                        Coupon(amount = acc.amount + coupon.amount)
                    }
                    ?.amount
                    ?: 0f

            val transactionSummary =
                getTransactionSummaryUseCase.invoke(
                    cartItems,
                    couponAmount,
                    uiState.payout.lotteryPayouts,
                    storeLevelConfig.value?.feeConfig,
                    uiState.transactionSummary
                )

            var availableOffers = listOf<AvailableOffer>()
            promotionsData?.let {
                availableOffers =
                    getPromotionUseCase.getAvailableOffers(
                        it,
                        cartItems.filterIsInstance<
                                TransactionItemWithPLUItem>(),
                        _appliedPromotions.value
                    )
            }

            var priceBookListValid = priceBookList.filter { it.showInDashboard }
            // Perform items reordering
            priceBookListValid = reorderItems(priceBookListValid, storeConfig)

            departments = departmentList
            val departmentListValid =
                departmentList.filter { it.showInDashboard }

            val payout =
                uiState.payout.copy(
                    lotteryNames = lotteryNames,
                    vendorNames = vendorNames
                )

            return@combineFlow initialState.copy(
                loading = loading,
                cartItems = cartItems,
                transactionSummary = transactionSummary,
                departmentList = departmentListValid,
                priceBookList = priceBookListValid,
                customerCount = customerCount.first,
                totalSales = customerCount.second,
                isPriceCheck = uiState.isPriceCheck,
                taxList = taxList,
                payout = payout,
                availableOffers = availableOffers
            )
        }
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(),
                initialValue = _uiState.value
            )

    fun onTxnHistoryClick(navController: NavController) =
        viewModelScope.launch {
            if (uiState.value.cartItems.isEmpty()) {
                navController.navigate(Routes.TxnHistory.route)
            } else {
                _toastMessage.emit(
                    ToastMessage("Please complete the current transaction")
                )
            }
        }

    fun onSkipAgeVerification() =
        viewModelScope.launch {
            _enableSkip.emit(true)
            dismissAgeVerificationDialog()
            _selectedPLUItem.value?.let { onPriceBookItemClick(it) }
                ?: _selectedDepartment.value?.let { onDepartmentClick(it) }
        }

    fun onPayoutClick() =
        viewModelScope.launch {
            EventUtils.logEvent(EventUtils.Events.PAYOUT_CLICK)
            _uiState.update {
                it.copy(
                    payout =
                    it.payout.copy(
                        showPayoutDialog = true,
                        enableVendor =
                        txnItems.value.isEmpty() &&
                                it.payout.lotteryPayouts
                                    .isEmpty(),
                        enableLottery = true
                    )
                )
            }
        }

    fun dismissPayoutDialog() =
        viewModelScope.launch {
            _uiState.update {
                it.copy(payout = it.payout.copy(showPayoutDialog = false))
            }
        }

    fun onPayoutNextClick(payout: Payout) =
        viewModelScope.launch {
            _cashDrawerOpenTime.emit(Date())

            try {
                printerXManager.validateCashDrawer(
                    onCashDrawerNotAvailable = {
                        _toastMessage.emit(
                            ToastMessage(
                                message =
                                "Cash drawer not detected, please connect the cash drawer",
                                type = ToastMessageType.Error
                            )
                        )
                    },
                    onCashDrawerAvailable = {
                        try {
                            if (txnItems.value.isEmpty() &&
                                _storeLevelConfig.value
                                    ?.quickLotteryFlow
                                    .isTrue()
                            ) {
                                it.openAsync()
                            }
                        } catch (e: Exception) {
                            EventUtils.recordException(e)
                        } finally {
                            _uiState.update { state ->
                                state.copy(
                                    payout =
                                    state.payout.copy(
                                        payout =
                                        payout
                                    ),
                                    numPadResult =
                                    if (payout.category ==
                                        PayoutType
                                            .Vendor
                                    )
                                        NumPadResult
                                            .VendorPayout
                                    else
                                        NumPadResult
                                            .LotteryPayout
                                )
                            }
                        }
                    }
                )
            } catch (e: Exception) {
                EventUtils.recordException(e)
                _toastMessage.emit(
                    ToastMessage(
                        e.message ?: "Something went wrong",
                        ToastMessageType.Error
                    )
                )
            }
        }

    fun dismissCouponDialog() =
        viewModelScope.launch { _uiState.update { it.copy(showCouponDialog = false) } }

    fun onCouponAvailable(coupon: Coupon) =
        viewModelScope.launch {
            var _coupon = coupon
            val list =
                mutableListOf<Coupon>().also {
                    it.addAll(_uiState.value.coupons)

                    if (coupon.name.isNullOrEmpty()) {
                        _coupon =
                            coupon.copy(
                                name =
                                "Coupon-${_currentCouponCount.value}"
                            )
                        _currentCouponCount.update { count -> count + 1 }
                    }

                    it.add(_coupon)
                }
            _uiState.update { it.copy(coupons = list) }
        }

    fun onCashDrawerCloseClick() =
        viewModelScope.launch {
            if (ENABLE_EMULATOR) {
                completeCashTransaction()
                return@launch
            }

            printerXManager.validateCashDrawer(
                onCashDrawerNotAvailable = {
                    _toastMessage.emit(
                        ToastMessage(
                            message =
                            "Cash drawer not detected, please connect the cash drawer",
                            type = ToastMessageType.Error
                        )
                    )
                },
                onCashDrawerAvailable = {
                    if (!it.isOpen) {
                        completeCashTransaction()
                    } else {
                        _toastMessage.emit(
                            ToastMessage(
                                message =
                                "Please close the cash drawer",
                                type = ToastMessageType.Error
                            )
                        )
                    }
                }
            )
        }

    private fun completeCashTransaction() =
        viewModelScope.launch {
            var uiState = _uiState.value
            if (uiState.cardPaymentState.amount > 0f) {
                uiState =
                    uiState.copy(
                        cardPaymentState =
                        uiState.cardPaymentState.copy(
                            completed = true
                        )
                    )
            }
            if (uiState.checkPaymentState.amount > 0f) {
                uiState =
                    uiState.copy(
                        checkPaymentState =
                        uiState.checkPaymentState.copy(
                            completed = true
                        )
                    )
            }
            if (uiState.creditPaymentState.amount > 0f) {
                uiState =
                    uiState.copy(
                        creditPaymentState =
                        uiState.creditPaymentState.copy(
                            completed = true
                        )
                    )
            }
            _uiState.update {
                uiState.copy(
                    cashPaymentState =
                    uiState.cashPaymentState.copy(
                        completed = true,
                        received = true
                    )
                )
            }
        }

    fun dismissEBTDialog() =
        viewModelScope.launch {
            _uiState.emit(
                _uiState.value.copy(showEBTDialog = false, selectedEbtType = null)
            )
        }

    private fun isValidTransaction(): Boolean =
        txnItems.value.any { it.status == TransactionItemStatus.Normal } ||
                uiState.value.payout.lotteryPayouts.isNotEmpty()

    private suspend fun transaction(): Transaction {
        var transaction = _currentTransaction.value
        val storeConfig = _storeConfig.value
        val cashierId = cashierId()

        // Ensure we get the most up-to-date transaction items by yielding to allow
        // StateFlow
        // emissions to complete
        yield()
        val currentTxnItems = txnItems.value

        if (transaction == null) {
            transaction =
                BaseTransaction(
                    txnId =
                    generateTransactionId(
                        storeCode = storeConfig?.storeCode ?: "",
                        posNumber = storeConfig?.posNumber ?: "1"
                    ),
                    txnItems = currentTxnItems,
                    txnStatus = TransactionStatus.Current,
                    statusHistory =
                    hashMapOf(
                        Date().epochInSeconds() to
                                TransactionStatus.Current
                    ),
                    txnType = TransactionType.Sale,
                    cashierId = cashierId
                )
            _currentTransaction.emit(transaction)
        }

        if (transaction is BaseTransaction) {
            transaction = transaction.copy(txnItems = currentTxnItems)
        }
        return transaction
    }

    private fun saveTransaction(transaction: Transaction) =
        viewModelScope.launch {
            val saveTransaction = transactionRepository.saveTransaction(transaction)
            if (saveTransaction) {
                resetTransactionSwapState(null)
            }
        }

    fun showToast(toastMessage: ToastMessage) =
        viewModelScope.launch { _toastMessage.emit(toastMessage) }

    fun clearToast() = viewModelScope.launch { _toastMessage.emit(null) }
    fun dismissUnderAgeDialog() =
        viewModelScope.launch { _uiState.emit(_uiState.value.copy(customerMinimumAge = 0)) }

    fun dismissNumPadDialog() =
        viewModelScope.launch {
            if (_uiState.value.numPadResult == NumPadResult.CashWithdrawal ||
                _uiState.value.numPadResult == NumPadResult.CashDeposit
            ) {
                resetTransactionSwapState(null)
            }
            _uiState.emit(_uiState.value.copy(numPadResult = null))
        }

    fun dismissAgeVerificationDialog() =
        viewModelScope.launch {
            _uiState.emit(_uiState.value.copy(showAgeVerificationDialog = false))
        }

    fun onCustomerAgeSubmit(date: String) =
        viewModelScope.launch {
            dismissAgeVerificationDialog()
            _customerAge.emit(date)
            _selectedPLUItem.value?.let {
                val minimumCustomerAge = it.minimumCustomerAge ?: 0
                if (isUnderAge(it.minimumCustomerAge, date)) {
                    _uiState.emit(
                        _uiState.value.copy(
                            customerMinimumAge = minimumCustomerAge
                        )
                    )
                } else {
                    onPriceBookItemClick(it)
                }
            }
                ?: _selectedDepartment.value?.let {
                    val minimumCustomerAge = it.minimumCustomerAge ?: 0
                    if (isUnderAge(it.minimumCustomerAge, date)) {
                        _uiState.emit(
                            _uiState.value.copy(
                                customerMinimumAge =
                                minimumCustomerAge
                            )
                        )
                    } else {
                        onDepartmentClick(it)
                    }
                }
        }

    fun onReportIssueClick(it: PluItem) =
        viewModelScope.launch {
            val reportMissingItem =
                gUPCCorrectionRepository.reportWrongItem(
                    ReportWrongItem(current = it)
                )

            if (reportMissingItem) {
                _toastMessage.emit(
                    ToastMessage(
                        message = "Reported successfully",
                        type = ToastMessageType.Success
                    )
                )
            }
        }

    private fun completePayoutEvent(amount: Float) =
        viewModelScope.launch {
            val payoutData = uiState.value.payout.payout ?: return@launch
            val txnStartTime = _cashDrawerOpenTime.value ?: return@launch

            val cashierId = cashierId()

            val payoutEvent =
                PayoutEvent(
                    txnId = transaction().txnId,
                    cashierId = cashierId,
                    txnStartTime = txnStartTime,
                    payoutData = payoutData.copy(amount = amount),
                    statusHistory =
                    hashMapOf(
                        Date().epochInSeconds() to
                                TransactionStatus.Complete
                    )
                )
            val success = transactionRepository.saveTransaction(payoutEvent)
            if (success) {
                printTransactionUseCase.printIfAllowed(
                    _storeConfig.value,
                    payoutEvent
                )
                resetTransactionSwapState(null)
                showToast("Txn saved successfully", ToastMessageType.Success)
            }
        }

    fun onKeypadOkPress(value: Float) =
        viewModelScope.launch {
            val amount = value / 100f

            var closeNumPad = true
            val storeConfig = _storeConfig.value
            val numPadResult = _uiState.value.numPadResult

            when (numPadResult) {
                NumPadResult.AddDepartmentItem -> {
                    if (txnItems.value.isEmpty()) {
                        // create base transaction
                        transaction()
                    }
                    val storeCode = storeConfig?.storeCode ?: ""

                    _selectedDepartment.value?.let { item ->
                        val txnItem =
                            TransactionItemWithDepartment(
                                department =
                                item.copy(
                                    departmentPrice =
                                    amount
                                ),
                                taxation =
                                getTaxForAmountMapUseCase(
                                    GetTaxMap(
                                        taxIds =
                                        item.taxes,
                                        storeCode =
                                        storeCode,
                                        itemPrice =
                                        amount,
                                        discount =
                                        0.0,
                                    )
                                )
                            )
                        dashboardStateProvider.addDepartmentTxnItem(
                            txnItem,
                            storeCode,
                            uiState.value.transactionSummary,
                            taxMap
                        )
                        launchAddLinkedPlus(
                            storeCode,
                            fromDepartment = item
                        )
                    }
                }

                NumPadResult.AgeValidation -> {}
                NumPadResult.CashTransaction -> onCashAmountClick(amount)
                NumPadResult.EditDepartmentPrice -> {}
                NumPadResult.EditPluQuantity -> {
                    val transactionItem =
                        _selectedTransactionItem.value ?: return@launch
                    val existingItems = txnItems.value
                    val quantity = value.toInt()

                    dashboardStateProvider.updateTxnItems(
                        when (transactionItem) {
                            is TransactionItemWithPLUItem -> {
                                val updatePbTxnItemResult =
                                    updatePriceBookTxnItemsUseCase(
                                        PriceBookUpdateQuantity(
                                            itemLine =
                                            transactionItem,
                                            quantity =
                                            quantity,
                                            cartItems =
                                            existingItems,
                                            storeCode =
                                            storeConfig
                                                ?.storeCode
                                                ?: "",
                                            transactionSummary =
                                            uiState.value
                                                .transactionSummary,
                                            taxes =
                                            taxMap
                                        )
                                    )
                                _appliedPromotions.emit(
                                    updatePbTxnItemResult
                                        .appliedPromos
                                )
                                updatePbTxnItemResult.txnItems
                            }

                            is TransactionItemWithDepartment ->
                                updateDepartmentTxnItemsUseCase(
                                    DepartmentUpdateQuantity(
                                        transactionItem,
                                        existingItems,
                                        storeConfig
                                            ?.storeCode
                                            ?: "",
                                        quantity,
                                        uiState.value
                                            .transactionSummary,
                                        taxes = taxMap
                                    )
                                )

                            is TransactionItemWithFuel -> existingItems
                        }
                    )
                    if (transactionItem is TransactionItemWithPLUItem) {
                        reCalculateLoyalty()
                    }
                }

                NumPadResult.CashCountInDrawer -> {

                    val cashierId = cashierId()

                    if (ENABLE_EMULATOR) {
                        val success =
                            eventRepository.logOut(
                                Event(
                                    eventId =
                                    generateTransactionId(
                                        storeConfig
                                            ?.storeCode
                                            ?: "",
                                        posNumber =
                                        storeConfig
                                            ?.posNumber
                                            ?: "1"
                                    ),
                                    event = EventType.Logout,
                                    eventStartTime =
                                    _cashDrawerOpenTime
                                        .value,
                                    eventEndTime = Date(),
                                    storeCode =
                                    storeConfig
                                        ?.storeCode
                                        ?: "",
                                    cashierId = cashierId,
                                    eventData =
                                    LogOutEventData(
                                        cashBalance =
                                        amount
                                    )
                                )
                            )

                        if (success) {
                            _logoutCashBalance.emit(amount)
                            _logOutEvent.emit(true)
                            delay(2000)
                            resetTransactionSwapState(null)
                        }

                        return@launch
                    }

                    printerXManager.validateCashDrawer(
                        onCashDrawerAvailable = {
                            closeNumPad = !it.isOpen

                            if (it.isOpen) {
                                // please close the drawer message
                                _toastMessage.emit(
                                    ToastMessage(
                                        message =
                                        "Please close the cash drawer to continue with log out"
                                    )
                                )
                            } else {

                                val success =
                                    eventRepository.logOut(
                                        Event(
                                            eventId =
                                            generateTransactionId(
                                                storeConfig
                                                    ?.storeCode
                                                    ?: "",
                                                posNumber =
                                                storeConfig
                                                    ?.posNumber
                                                    ?: "1"
                                            ),
                                            event =
                                            EventType
                                                .Logout,
                                            eventStartTime =
                                            _cashDrawerOpenTime
                                                .value,
                                            eventEndTime =
                                            Date(),
                                            storeCode =
                                            storeConfig
                                                ?.storeCode
                                                ?: "",
                                            cashierId =
                                            cashierId,
                                            eventData =
                                            LogOutEventData(
                                                cashBalance =
                                                amount
                                            )
                                        )
                                    )
                                if (success) {
                                    _logoutCashBalance.emit(
                                        amount
                                    )
                                    _logOutEvent.emit(true)
                                    delay(2000)
                                    resetTransactionSwapState(
                                        null
                                    )
                                }
                            }
                        },
                        onCashDrawerNotAvailable = {
                            _toastMessage.emit(
                                ToastMessage(
                                    message =
                                    "Cash drawer is not available, Please connect to log out"
                                )
                            )
                        }
                    )
                }

                NumPadResult.LotteryPayout -> {

                    if (txnItems.value.isEmpty() &&
                        _storeLevelConfig.value?.quickLotteryFlow
                            .isTrue()
                    ) {
                        if (ENABLE_EMULATOR) {
                            completePayoutEvent(amount)
                            return@launch
                        }

                        printerXManager.validateCashDrawer(
                            onCashDrawerAvailable = {
                                closeNumPad = !it.isOpen

                                if (it.isOpen) {
                                    // please close the drawer
                                    // message
                                    _toastMessage.emit(
                                        ToastMessage(
                                            message =
                                            "Please close the cash drawer to continue"
                                        )
                                    )
                                } else {
                                    completePayoutEvent(amount)
                                }
                            },
                            onCashDrawerNotAvailable = {
                                _toastMessage.emit(
                                    ToastMessage(
                                        message =
                                        "Cash drawer is not available, Please connect to log out"
                                    )
                                )
                            }
                        )
                    } else {
                        val lotteryPayout =
                            LotteryPayout(
                                linkedTxnId =
                                generateTransactionId(
                                    storeConfig
                                        ?.storeCode
                                        ?: "",
                                    posNumber =
                                    storeConfig
                                        ?.posNumber
                                        ?: "1"
                                ),
                                amount = amount,
                                info =
                                _uiState.value
                                    .payout
                                    .payout
                                    ?.info
                                    ?: ""
                            )
                        _uiState.update {
                            it.copy(
                                payout =
                                it.payout.copy(
                                    lotteryPayouts =
                                    it.payout
                                        .lotteryPayouts
                                        .toMutableList()
                                        .apply {
                                            add(
                                                lotteryPayout
                                            )
                                        }
                                ),
                            )
                        }
                    }
                }

                NumPadResult.VendorPayout -> {

                    if (ENABLE_EMULATOR) {
                        completePayoutEvent(amount)
                        return@launch
                    }

                    printerXManager.validateCashDrawer(
                        onCashDrawerAvailable = {
                            closeNumPad = !it.isOpen

                            if (it.isOpen) {
                                // please close the drawer message
                                _toastMessage.emit(
                                    ToastMessage(
                                        message =
                                        "Please close the cash drawer to continue"
                                    )
                                )
                            } else {
                                completePayoutEvent(amount)
                            }
                        },
                        onCashDrawerNotAvailable = {
                            _toastMessage.emit(
                                ToastMessage(
                                    message =
                                    "Cash drawer is not available, Please connect to log out"
                                )
                            )
                        }
                    )
                }

                NumPadResult.FuelPrepay, NumPadResult.FuelPreset -> {
                    if (txnItems.value.isEmpty()) {
                        // create base transaction
                        transaction()
                    }
                }

                NumPadResult.RestInGas -> {
                    if (txnItems.value.isEmpty()) {
                        // create base transaction
                        transaction()
                    }
                }

                NumPadResult.MovePrepay -> {}
                NumPadResult.LoyaltyNumber -> {
                    val phoneInput = value.toInt().toString()
                    if (phoneInput.isNotEmpty() &&
                        phoneInput.length >= 10 &&
                        phoneInput.length <= 11
                    ) {
                        updateLoyaltyAccountId(value.toInt().toString())
                    } else {
                        showToast(
                            "Invalid Phone Number",
                            ToastMessageType.Error
                        )
                    }
                }

                NumPadResult.CashWithdrawal, NumPadResult.CashDeposit -> {
                    closeNumPad = true
                    updateCashAdjustmentTxn(numPadResult, amount)
                }

                else -> {}
            }
            if (closeNumPad) {
                dismissNumPadDialog()
            }
        }

    private fun cashDrawerCloseCheck(onClosedCallback: () -> Unit) =
        viewModelScope.launch {
            printerXManager.validateCashDrawer(
                onCashDrawerAvailable = {
                    if (it.isOpen) {
                        // please close the drawer message
                        _toastMessage.emit(
                            ToastMessage(
                                message =
                                "Please close the cash drawer to continue"
                            )
                        )
                    } else {
                        onClosedCallback.invoke()
                    }
                },
                onCashDrawerNotAvailable = {
                    showToast(
                        "Cash drawer not detected, please connect the cash drawer",
                        ToastMessageType.Error
                    )
                }
            )
        }

    fun cashDrawerOpenCheck(
        onOpenCallback: () -> Unit,
        onFailureCallback: (() -> Unit)? = null
    ) =
        viewModelScope.launch {
            printerXManager.validateCashDrawer(
                onCashDrawerAvailable = {
                    try {
                        it.openAsync()
                    } catch (e: Exception) {
                        showToast(
                            e.message ?: "Couldn't open the drawer",
                            ToastMessageType.Error
                        )
                        EventUtils.recordException(e)
                        onFailureCallback?.invoke()
                    } finally {
                        onOpenCallback.invoke()
                    }
                },
                onCashDrawerNotAvailable = {
                    _toastMessage.emit(
                        ToastMessage(
                            message =
                            "Cash drawer is not available, Please connect to log out"
                        )
                    )
                }
            )
        }

    private fun saveCashAdjustmentTxn(numPadResult: NumPadResult) =
        viewModelScope.launch {
            if (_loadings.value.contains(Loadings.SAVING_TXN)) {
                return@launch
            }
            _loadings.update { it.toMutableSet().apply { add(Loadings.SAVING_TXN) } }
            val txnStartTime = _cashDrawerOpenTime.value ?: return@launch
            val cashierId = cashierId()
            val txn =
                when (numPadResult) {
                    NumPadResult.CashWithdrawal ->
                        CashWithdrawalTxn(
                            txnId = transaction().txnId,
                            txnStartTime = txnStartTime,
                            cashierId = cashierId,
                            data = CashWithdrawalData(),
                            txnStatus = TransactionStatus.Pending,
                            statusHistory =
                            hashMapOf(
                                Date().epochInSeconds() to
                                        TransactionStatus
                                            .Pending
                            )
                        )

                    NumPadResult.CashDeposit ->
                        CashDepositTxn(
                            txnId = transaction().txnId,
                            txnStartTime = txnStartTime,
                            cashierId = cashierId,
                            data = CashDepositData(),
                            txnStatus = TransactionStatus.Pending,
                            statusHistory =
                            hashMapOf(
                                Date().epochInSeconds() to
                                        TransactionStatus
                                            .Pending
                            )
                        )

                    else -> null
                }
            txn?.let {
                transactionRepository.saveTransaction(it)
                _currentTransaction.emit(it)
            }
            _loadings.update { it.toMutableSet().apply { remove(Loadings.SAVING_TXN) } }
        }

    private fun updateCashAdjustmentTxn(numPadResult: NumPadResult, amount: Float) =
        viewModelScope.launch {
            _currentTransaction.value.let {
                val success =
                    when (numPadResult) {
                        is NumPadResult.CashWithdrawal -> {
                            val txn =
                                (it as CashWithdrawalTxn).copy(
                                    data =
                                    it.data.copy(
                                        amount =
                                        amount
                                    ),
                                    txnStatus =
                                    TransactionStatus
                                        .Complete,
                                    statusHistory =
                                    HashMap(
                                        it.statusHistory
                                            .orEmpty()
                                    )
                                        .apply {
                                            put(
                                                Date().epochInSeconds(),
                                                TransactionStatus
                                                    .Complete
                                            )
                                        }
                                )
                            val saved =
                                transactionRepository
                                    .saveTransaction(txn)
                            if (saved) {
                                printTransactionUseCase
                                    .printIfAllowed(
                                        _storeConfig.value,
                                        txn
                                    )
                            }
                            saved
                        }

                        is NumPadResult.CashDeposit -> {
                            val txn =
                                (it as CashDepositTxn).copy(
                                    data =
                                    it.data.copy(
                                        amount =
                                        amount
                                    ),
                                    txnStatus =
                                    TransactionStatus
                                        .Complete,
                                    statusHistory =
                                    HashMap(
                                        it.statusHistory
                                            .orEmpty()
                                    )
                                        .apply {
                                            put(
                                                Date().epochInSeconds(),
                                                TransactionStatus
                                                    .Complete
                                            )
                                        }
                                )
                            val saved =
                                transactionRepository
                                    .saveTransaction(txn)
                            if (saved) {
                                printTransactionUseCase
                                    .printIfAllowed(
                                        _storeConfig.value,
                                        txn
                                    )
                            }
                            saved
                        }

                        else -> null
                    }
                if (success.isTrue()) {
                    showToast(
                        "Txn saved successfully",
                        ToastMessageType.Success
                    )
                } else {
                    showToast("Failed to save txn", ToastMessageType.Error)
                }
                resetTransactionSwapState(null)
            }
        }

    fun onEBTProceedClick(finishTransaction: Boolean, payAmount: Float? = null) =
        viewModelScope.launch {
            val storeLevelConfig = _storeLevelConfig.value
            val storeConfig = _storeConfig.value
            if (!isValidTransaction() || storeConfig == null || storeLevelConfig == null
            ) {
                return@launch
            }
            if (storeLevelConfig.ebtPayments && storeLevelConfig.streamPayments) {
                _uiState.value.selectedEbtType?.let {
                    _uiState.emit(_uiState.value.copy(showEBTDialog = false))
                    if (storeLevelConfig.payfac == "epx") {
                        processEbtWithEpx(
                            it,
                            EpxTransactionType.SALE,
                            payAmount
                        )
                    } else {
                        processEbtPayment(it, payAmount)
                    }
                    return@launch
                }
            }
            val value = _txnPayment.value
            val uiStateRef = uiState.value
            var transactionSummary = uiStateRef.transactionSummary
            val ebtAmountCollected = transactionSummary.totalEBT()
            value[TxnPaymentType.EBT] = EBTPayment(ebtAmountCollected, payFacId = "")

            transactionSummary =
                transactionSummary.copy(
                    ebtAmountCollected = ebtAmountCollected,
                )
            val updatePbTxnItemResult =
                updatePriceBookTxnItemsUseCase(
                    JustUpdateItemLines(
                        cartItems = txnItems.value,
                        transactionSummary = transactionSummary,
                        storeCode = storeConfig.storeCode,
                        taxes = taxMap
                    )
                )
            _appliedPromotions.emit(updatePbTxnItemResult.appliedPromos)
            dashboardStateProvider.updateTxnItems(updatePbTxnItemResult.txnItems)

            _txnPayment.emit(value)
            _uiState.emit(
                uiStateRef.copy(
                    transactionSummary = transactionSummary,
                    showEBTDialog = false,
                    ebtPaymentState =
                    uiStateRef.ebtPaymentState.copy(
                        completed = true,
                        amount = ebtAmountCollected,
                        received = true
                    )
                )
            )

            if (finishTransaction) {
                delay(500)

                onCashAmountClick(
                    transactionSummary.transactionTotalGrandAmount -
                            ebtAmountCollected
                )
            }
        }

    fun cashierId() = currentUser.value?.username ?: "cashierId"

    private var paymentJob: Job? = null

    fun updatePriceAndInitiateCard(fdcState: FDCState) =
        viewModelScope.launch {
            hideFuelPriceDiff()
            uiState.value.updatedFuelItems?.let {
                if (it.isNotEmpty()) {
                    dashboardStateProvider.updateTxnItems(it)
                    delay(500)
                    onCardClick(_uiState.value.enteredCardAmount, fdcState)
                }
            }
        }

    fun onCardClick(enteredAmount: Float? = null, fdcState: FDCState) =
        viewModelScope.launch {
            EventUtils.logEvent(
                EventUtils.Events.CARD_CLICK,
                mapOf(EventUtils.EventProp.AMOUNT to (enteredAmount ?: 0f))
            )
            if (enteredAmount != null) {
                _uiState.update { it.copy(enteredCardAmount = enteredAmount) }
                hideCardPaymentDialog()
            }
            /*
                           Currently we are supporting a single card payment.
                        */
            if (_txnPayment.value[TxnPaymentType.Card] != null) {
                showToast(
                    "Currently, a single card payment is supported",
                    ToastMessageType.Info
                )
                return@launch
            }
            val storeConfig = _storeConfig.value
            val storeLevelConfig = _storeLevelConfig.value
            if (!isValidTransaction() || storeConfig == null || storeLevelConfig == null
            ) {
                return@launch
            }
            /*
                           Validate if the price for fuel is matching.
                           If cash payment is present, ignore this check because we will charge them
                            based on cash price which is default, so there shouldn't be any difference.
                        */
            if (_txnPayment.value[TxnPaymentType.Card] == null) {
                val result =
                    validateFuelPriceUseCase.priceDifferenceAndUpdateItems(
                        txnItems.value,
                        TxnPaymentType.Card,
                        fdcState.dispensers
                    )
                if (result.first > 0.01) {
                    _uiState.update {
                        it.copy(
                            fuelPriceDiff = result.first,
                            updatedFuelItems = result.second
                        )
                    }
                    return@launch
                }
            }

            val cashierId = cashierId()
            val state = uiState.value

            val epoch = getEpoch()

            val attempt = state.paymentState.attempts + 1
            val transactionSummary = state.transactionSummary
            val amount = enteredAmount ?: transactionSummary.pendingAmount()
            // Use the same txnItems that were used to calculate the transaction summary
            // to ensure consistency between amount calculation and fuel detection
            val currentTxnItems = state.cartItems
            val transaction =
                transaction().let { txn ->
                    if (txn is BaseTransaction) {
                        txn.copy(txnItems = currentTxnItems)
                    } else txn
                }
            val cardFee =
                GetFeesUseCase.getCardProcessingFee(
                    amount,
                    storeLevelConfig.feeConfig
                )
            val appliedFees = mutableListOf<AppliedFee>()
            cardFee?.let { appliedFees.add(it) }
            _uiState.update {
                it.copy(paymentState = it.paymentState.copy(attempts = attempt))
            }
            try {
                val token =
                    hOtpGenerator.generateHOTP(
                        tid = transaction.txnId,
                        token = storeConfig.token,
                        counter = storeConfig.posNumber.toLong()
                    )
                if (storeLevelConfig.payfac == "epx") {
                    processPaymentWithEpx(
                        transaction.txnId,
                        amount,
                        cashierId,
                        appliedFees
                    )
                } else {
                    processPaymentWithBE(
                        transaction.txnId,
                        amount,
                        cashierId,
                        epoch,
                        token,
                        attempt,
                        transaction,
                        appliedFees
                    )
                }
            } catch (e: Exception) {
                EventUtils.recordException(e)
            }
        }

    private fun processPaymentWithBE(
        txnId: String,
        amount: Float,
        cashierId: String,
        epoch: Int,
        token: String,
        attempt: Int,
        transaction: Transaction,
        appliedFees: List<AppliedFee>
    ) =
        viewModelScope.launch {
            val storeConfig = _storeConfig.value
            val storeLevelConfig = _storeLevelConfig.value
            if (!isValidTransaction() || storeConfig == null || storeLevelConfig == null
            ) {
                return@launch
            }
            val fees = GetFeesUseCase.getTotalAppliedFee(appliedFees)
            val cents = (amount.plus(fees)).toDouble().convertDollarToCentPrecisely()
            savePendingTransaction(transaction)
            if (storeLevelConfig.streamPayments) {
                val paymentInput =
                    PaymentInputDTO(
                        epoch,
                        TData(
                            token,
                            cents,
                            txnId,
                            attempt,
                            TxnType.merchandise,
                            PaymentType.gen,
                            0
                        )
                    )
                startPaymentStream(
                    amount,
                    paymentInput,
                    storeConfig.posId,
                    cashierId,
                    transaction,
                    appliedFees
                )
            } else {
                startPaymentApi(
                    cents,
                    amount,
                    transaction,
                    attempt,
                    token,
                    cashierId,
                    epoch,
                    storeLevelConfig,
                    appliedFees
                )
            }
        }

    private fun processPaymentWithEpx(
        txnId: String,
        amount: Float,
        cashierId: String,
        appliedFees: List<AppliedFee>
    ) =
        viewModelScope.launch {
            if (_loadings.value.contains(Loadings.PAYMENT_PROCESS)) {
                return@launch
            }
            _loadings.update {
                it.toMutableSet().apply { add(Loadings.PAYMENT_PROCESS) }
            }
            val storeLevelConfig = _storeLevelConfig.value
            var transaction = transaction()
            if (transaction is BaseTransaction) {
                transaction =
                    transaction.copy(
                        txnStatus = TransactionStatus.Pending,
                        statusHistory =
                        HashMap(transaction.statusHistory.orEmpty())
                            .apply {
                                put(
                                    Date().epochInSeconds(),
                                    TransactionStatus
                                        .Pending
                                )
                            },
                    )
            }
            _currentTransaction.emit(transaction)
            val fees = GetFeesUseCase.getTotalAppliedFee(appliedFees)
            val cents =
                (amount.plus(fees))
                    .toDouble()
                    .convertDollarToCentPrecisely()
                    .toString()
            var paxPaymentInput =
                PaxPaymentInput(
                    txnId,
                    paxPaymentService.getCurrentBatchId(),
                    cents,
                    cashierId,
                    Date().epochInSeconds()
                )
            // Only "Authorize" if it's a prepay fuel transaction, don't capture it.
            if (transaction.hasFuel && !transaction.hasPostFuel) {
                paxPaymentInput =
                    paxPaymentInput.copy(
                        txnType = EpxTransactionType.AUTHORIZATION
                    )
            }
            paymentUseCase
                .processEpxCreditRequest(
                    paxPaymentInput,
                    convertToSaleTransaction(
                        transaction,
                        _uiState.value.transactionSummary
                    )
                )
                .stateIn(viewModelScope)
                .collect { result ->
                    when (result) {
                        is Result.Loading ->
                            _isLoading.emit(
                                "Payment is being processed..."
                            )

                        is Result.Error -> {
                            handlePaymentFailure(
                                result.errorMessage,
                                result.errorCode,
                                transaction.txnId,
                                storeLevelConfig?.autoRetryPayments,
                                false,
                                0
                            )
                            _loadings.update {
                                it.toMutableSet().apply {
                                    remove(
                                        Loadings.PAYMENT_PROCESS
                                    )
                                }
                            }
                        }

                        is Result.Success -> {
                            result.data?.let {
                                val cardPayment =
                                    CardPayment(
                                        amount = amount,
                                        payFacId =
                                        it.hostInformation
                                            ?.hostReferenceNumber
                                            .orEmpty(),
                                        brand =
                                        it.accountInformation
                                            ?.cardType
                                            ?.name
                                            .orEmpty(),
                                        paymentType =
                                        it.paymentEmvTag
                                            ?.appLabel
                                            .orEmpty(),
                                        epxData =
                                        EpxData(
                                            transactionType =
                                            paxPaymentInput
                                                .txnType,
                                            amount =
                                            amount,
                                            it.hostInformation
                                                ?.authorizationCode
                                                .orEmpty(),
                                            it.hostInformation
                                                ?.hostResponseMessage
                                                .orEmpty(),
                                            it.hostInformation
                                                ?.hostReferenceNumber
                                                .orEmpty(),
                                        ),
                                        epxAuthData =
                                        if (paxPaymentInput
                                                .txnType ==
                                            EpxTransactionType
                                                .AUTHORIZATION
                                        ) {
                                            EpxData(
                                                transactionType =
                                                paxPaymentInput
                                                    .txnType,
                                                amount =
                                                amount,
                                                it.hostInformation
                                                    ?.authorizationCode
                                                    .orEmpty(),
                                                it.hostInformation
                                                    ?.hostResponseMessage
                                                    .orEmpty(),
                                                it.hostInformation
                                                    ?.hostReferenceNumber
                                                    .orEmpty(),
                                            )
                                        } else {
                                            null
                                        }
                                    )
                                val cardInfo =
                                    CardInfo(
                                        merchantId =
                                        it.hostInformation
                                            ?.paymentAccountReferenceId
                                            .orEmpty(),
                                        approvalCode =
                                        it.hostInformation
                                            ?.authorizationCode
                                            .orEmpty(),
                                        logo =
                                        it.accountInformation
                                            ?.cardType
                                            ?.name
                                            .orEmpty(),
                                        transactionId =
                                        txnId,
                                        account =
                                        it.accountInformation
                                            ?.account
                                            .orEmpty(),
                                        entry =
                                        it.accountInformation
                                            ?.entryMode
                                            ?.name
                                            .orEmpty(),
                                        label =
                                        it.paymentEmvTag
                                            ?.appLabel
                                            .orEmpty(),
                                    )
                                _toastMessage.emit(
                                    ToastMessage(
                                        it.responseMessage
                                            ?: "Payment Successful",
                                        ToastMessageType
                                            .Success
                                    )
                                )
                                _uiState.update {
                                    it.copy(
                                        appliedFees =
                                        appliedFees
                                    )
                                }
                                _isLoading.emit(null)
                                val txnWithPaymentRecord =
                                    transactionRepository
                                        .getTransaction(
                                            transaction
                                                .txnId
                                        )
                                handleCardPaymentSuccess(
                                    cardPayment,
                                    cardInfo,
                                    amount,
                                    transaction,
                                    txnWithPaymentRecord
                                )
                                _loadings.update {
                                    it.toMutableSet().apply {
                                        remove(
                                            Loadings.PAYMENT_PROCESS
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
        }

    private fun startPaymentStream(
        amount: Float,
        paymentInput: PaymentInputDTO,
        posId: String,
        cashierId: String,
        transaction: Transaction,
        appliedFees: List<AppliedFee> = listOf()
    ) {
        viewModelScope.launch {
            if (paymentJob?.isActive == true) {
                _toastMessage.emit(
                    ToastMessage(
                        "Another payment in process.",
                        ToastMessageType.Error
                    )
                )
                return@launch
            }
            paymentJob = launch {
                _uiState.update { ui ->
                    ui.copy(
                        paymentResponseWS =
                        PaymentResponseWS(
                            title = "Connecting to server",
                            msg =
                            "Establishing a secure connection to the server."
                        ),
                        transactionId = transaction.txnId
                    )
                }
                paymentProcessor.processPayment(paymentInput, posId, cashierId)
                    .collectLatest {
                        if (_currentTransaction.value?.txnId !=
                            transaction.txnId
                        ) {
                            return@collectLatest
                        }
                        _uiState.update { uiState ->
                            var newState =
                                uiState.copy(paymentResponseWS = it)
                            paymentInput.tData.ebtType?.let { ebtType ->
                                newState =
                                    newState.copy(
                                        ebtBalancesCents =
                                        newState.ebtBalancesCents
                                            .apply {
                                                newState.ebtBalancesCents[
                                                    ebtType] =
                                                    it.info
                                                        ?.cardBalanceCents
                                            }
                                    )
                            }
                            newState
                        }
                        if (it.completed == true) {
                            paymentJob?.cancel()
                            _uiState.emit(
                                _uiState.value.copy(
                                    selectedEbtType = null
                                )
                            )
                        }
                        if (it.isSuccess()) {
                            _uiState.update {
                                it.copy(appliedFees = appliedFees)
                            }
                            if (paymentInput.tData.paymentType ==
                                PaymentType.ebt
                            ) {
                                val ebtPayment =
                                    EBTPayment(
                                        amount,
                                        payFacId =
                                        it.info
                                            ?.payfacId
                                            .orEmpty()
                                    )
                                val ebtInfo =
                                    EbtInfo(
                                        merchantId =
                                        it.info
                                            ?.mid,
                                        auth =
                                        it.info
                                            ?.payfacId,
                                        ref =
                                        it.info
                                            ?.approvalCode,
                                        cardNo =
                                        it.info
                                            ?.cardNumber,
                                        cardEntryMode =
                                        it.info
                                            ?.cardEntryMode,
                                        balanceInCents =
                                        (it.info
                                            ?.cardBalanceCents
                                            ?: 0f)
                                            .toInt(),
                                        ebtType =
                                        paymentInput
                                            .tData
                                            .ebtType
                                    )
                                handleEbtPaymentSuccess(
                                    ebtPayment,
                                    ebtInfo,
                                    amount,
                                    transaction
                                )
                            } else {
                                val cardPayment =
                                    CardPayment(
                                        amount = amount,
                                        payFacId =
                                        it.info
                                            ?.cardNumber
                                            .orEmpty(),
                                        brand =
                                        it.info
                                            ?.cardBrand
                                            .orEmpty(),
                                        paymentType =
                                        it.info
                                            ?.paymentType
                                            .orEmpty(),
                                    )
                                val cardInfo =
                                    CardInfo(
                                        merchantId =
                                        it.info?.mid
                                            .orEmpty(),
                                        approvalCode =
                                        it.info
                                            ?.approvalCode
                                            .orEmpty(),
                                        logo =
                                        it.info
                                            ?.cardBrand
                                            .orEmpty(),
                                        transactionId =
                                        transaction
                                            .txnId,
                                        account =
                                        it.info
                                            ?.cardNumber
                                            .orEmpty(),
                                        entry =
                                        it.info
                                            ?.cardEntryMode
                                            .orEmpty(),
                                        label =
                                        it.info
                                            ?.applicationLabel
                                            .orEmpty(),
                                    )
                                handleCardPaymentSuccess(
                                    cardPayment,
                                    cardInfo,
                                    amount,
                                    transaction
                                )
                            }
                            if (_storeLevelConfig
                                    .value
                                    ?.confirmOnSuccessfulPayment !=
                                true
                            ) {
                                showToast(
                                    "Payment Successful",
                                    ToastMessageType.Success
                                )
                            }
                        }
                        if (it.isFailure()) {
                            viewModelScope.launch {
                                (_currentTransaction.value as?
                                        SaleTransaction)
                                    ?.let {
                                        val txn =
                                            it.copy(
                                                statusReason =
                                                StatusReason
                                                    .PAYMENT_FAILED
                                            )
                                        _currentTransaction
                                            .emit(txn)
                                        transactionRepository
                                            .saveTransaction(
                                                txn
                                            )
                                    }
                            }
                        }
                    }
            }
        }
    }

    private fun handleCardPaymentSuccess(
        cardPayment: CardPayment,
        cardInfo: CardInfo,
        amount: Float,
        txn: Transaction,
        txnWithPaymentRecord: Transaction? = null
    ) =
        viewModelScope.launch {
            val transactionSummary =
                uiState.value.transactionSummary.copy(cardAmountCollected = amount)
            val txnPayment = _txnPayment.value
            txnPayment[TxnPaymentType.Card] = cardPayment
            _txnPayment.emit(txnPayment)
            _cardInfo.emit(cardInfo)
            val txnSummary =
                getTransactionSummaryUseCase(
                    txnItems.value,
                    transactionSummary.couponAmount,
                    uiState.value.payout.lotteryPayouts,
                    storeLevelConfig.value?.feeConfig,
                    transactionSummary
                )
            // Add EBT info in the txn
            val dob = _customerAge.value
            val txnItems = txnItems.value
            val state = _uiState.value
            val coupons = state.coupons
            val loyaltyId = state.loyaltyState.loyaltyId
            val txnStatus =
                if (txnSummary.pendingAmount() == 0f) {
                    TransactionStatus.Complete
                } else {
                    TransactionStatus.PartiallyPaid
                }
            val saleTxn =
                if (txn is SaleTransaction) {
                    txn.copy(
                        txnEndTime = Date(),
                        txnTotalGrandAmount =
                        txnSummary.transactionTotalGrandAmount,
                        txnTotalGrossAmount =
                        txnSummary.transactionTotalGrossAmount,
                        txnTotalNetAmount =
                        txnSummary.transactionTotalNetAmount,
                        txnTotalTaxNetAmount =
                        txnSummary.transactionTotalTaxNetAmount,
                        txnStatus = txnStatus,
                        statusHistory =
                        HashMap(txn.statusHistory.orEmpty()).apply {
                            put(
                                Date().epochInSeconds(),
                                txnStatus
                            )
                        },
                        txnPayment = txnPayment,
                        cardInfo = cardInfo,
                        paymentRecord = txnWithPaymentRecord?.paymentRecord,
                        appliedFees = state.appliedFees
                    )
                } else {
                    SaleTransaction(
                        txnEndTime = Date(),
                        txnStartTime = txn.txnStartTime,
                        txnTotalGrandAmount =
                        txnSummary.transactionTotalGrandAmount,
                        txnTotalGrossAmount =
                        txnSummary.transactionTotalGrossAmount,
                        txnTotalNetAmount =
                        txnSummary.transactionTotalNetAmount,
                        txnTotalTaxNetAmount =
                        txnSummary.transactionTotalTaxNetAmount,
                        dob = dob,
                        cashierId = txn.cashierId,
                        txnStatus = txnStatus,
                        statusHistory =
                        HashMap(txn.statusHistory.orEmpty()).apply {
                            put(
                                Date().epochInSeconds(),
                                txnStatus
                            )
                        },
                        txnItems = txnItems,
                        txnId = txn.txnId,
                        txnPayment = txnPayment,
                        txnDiscount = TxnDiscount(coupons),
                        lotteryPayouts = state.payout.lotteryPayouts,
                        accountInfo =
                        if (loyaltyId == null) null
                        else
                            LoyaltyAccountInfo(
                                loyaltyId = loyaltyId
                            ),
                        cardInfo = cardInfo,
                        paymentRecord = txnWithPaymentRecord?.paymentRecord,
                        appliedFees = state.appliedFees
                    )
                }
            _currentTransaction.emit(saleTxn)
            transactionRepository.saveTransaction(saleTxn)
            var uiStateRef = _uiState.value
            if (txnSummary.pendingAmount() <= 0f) {
                if (uiStateRef.cashPaymentState.amount > 0f) {
                    uiStateRef =
                        uiStateRef.copy(
                            cashPaymentState =
                            uiStateRef.cashPaymentState.copy(
                                completed = true,
                                received = true
                            )
                        )
                }
                if (txnPayment[TxnPaymentType.Cash] is CashPayment) {
                    openCashDrawer()
                }
            }
            _uiState.update {
                uiStateRef.copy(
                    cardPaymentState =
                    it.cardPaymentState.copy(
                        completed =
                        txnSummary.pendingAmount() == 0f,
                        amount = amount,
                        received = true
                    ),
                    transactionSummary = txnSummary,
                    paymentState = PaymentState()
                )
            }
        }

    private fun handleEbtPaymentSuccess(
        ebtPayment: EBTPayment,
        ebtInfo: EbtInfo,
        amount: Float,
        txn: Transaction,
        txnWithPaymentRecord: Transaction? = null
    ) =
        viewModelScope.launch {
            <EMAIL> = ebtInfo
            val storeConfig = _storeConfig.value ?: return@launch
            var transactionSummary = uiState.value.transactionSummary
            val txnPayment = _txnPayment.value
            txnPayment[TxnPaymentType.EBT] = ebtPayment
            transactionSummary = transactionSummary.copy(ebtAmountCollected = amount)
            val updatePbTxnItemResult =
                updatePriceBookTxnItemsUseCase(
                    JustUpdateItemLines(
                        cartItems = txnItems.value,
                        transactionSummary = transactionSummary,
                        storeCode = storeConfig.storeCode,
                        taxes = taxMap
                    )
                )
            _appliedPromotions.emit(updatePbTxnItemResult.appliedPromos)
            var cartItems = updatePbTxnItemResult.txnItems
            cartItems =
                updateDepartmentTxnItemsUseCase(
                    RecalculateAmount(
                        cartItems = cartItems,
                        transactionSummary = transactionSummary,
                        storeCode = storeConfig.storeCode,
                        taxes = taxMap
                    )
                )
            val txnSummary =
                getTransactionSummaryUseCase(
                    cartItems,
                    transactionSummary.couponAmount,
                    uiState.value.payout.lotteryPayouts,
                    storeLevelConfig.value?.feeConfig,
                    transactionSummary
                )

            val cashPayment = txnPayment[TxnPaymentType.Cash]
            (cashPayment as? CashPayment)?.let {
                txnPayment[TxnPaymentType.Cash] =
                    it.copy(change = txnSummary.change())
            }
            _txnPayment.emit(txnPayment)
            dashboardStateProvider.updateTxnItems(cartItems)
            _uiState.update {
                it.copy(
                    transactionSummary = txnSummary,
                    showEBTDialog = false,
                    ebtPaymentState =
                    it.ebtPaymentState.copy(
                        completed = true,
                        amount = amount,
                        received = true
                    ),
                    paymentState = PaymentState()
                )
            }
            // Add EBT info in the txn
            val dob = _customerAge.value
            val txnItems = txnItems.value
            val state = _uiState.value
            val coupons = state.coupons
            val loyaltyId = state.loyaltyState.loyaltyId
            val txnStatus =
                if (txnSummary.pendingAmount() == 0f) {
                    TransactionStatus.Complete
                } else {
                    TransactionStatus.PartiallyPaid
                }
            val saleTxn =
                if (txn is SaleTransaction) {
                    txn.copy(
                        txnEndTime = Date(),
                        txnTotalGrandAmount =
                        txnSummary.transactionTotalGrandAmount,
                        txnTotalGrossAmount =
                        txnSummary.transactionTotalGrossAmount,
                        txnTotalNetAmount =
                        txnSummary.transactionTotalNetAmount,
                        txnTotalTaxNetAmount =
                        txnSummary.transactionTotalTaxNetAmount,
                        txnStatus = txnStatus,
                        statusHistory =
                        HashMap(txn.statusHistory.orEmpty()).apply {
                            put(
                                Date().epochInSeconds(),
                                txnStatus
                            )
                        },
                        txnPayment = txnPayment,
                        ebtInfo = ebtInfo,
                        paymentRecord = txnWithPaymentRecord?.paymentRecord
                    )
                } else {
                    SaleTransaction(
                        txnEndTime = Date(),
                        txnStartTime = txn.txnStartTime,
                        txnTotalGrandAmount =
                        txnSummary.transactionTotalGrandAmount,
                        txnTotalGrossAmount =
                        txnSummary.transactionTotalGrossAmount,
                        txnTotalNetAmount =
                        txnSummary.transactionTotalNetAmount,
                        txnTotalTaxNetAmount =
                        txnSummary.transactionTotalTaxNetAmount,
                        dob = dob,
                        cashierId = txn.cashierId,
                        txnStatus = txnStatus,
                        statusHistory =
                        HashMap(txn.statusHistory.orEmpty()).apply {
                            put(
                                Date().epochInSeconds(),
                                txnStatus
                            )
                        },
                        txnItems = txnItems,
                        txnId = txn.txnId,
                        txnPayment = txnPayment,
                        txnDiscount = TxnDiscount(coupons),
                        lotteryPayouts = state.payout.lotteryPayouts,
                        accountInfo =
                        if (loyaltyId == null) null
                        else
                            LoyaltyAccountInfo(
                                loyaltyId = loyaltyId
                            ),
                        ebtInfo = ebtInfo,
                        paymentRecord = txnWithPaymentRecord?.paymentRecord
                    )
                }
            _currentTransaction.emit(saleTxn)
            transactionRepository.saveTransaction(saleTxn)
            // If no amount is pending to be collected, clear the txn state
            if (txnSummary.pendingAmount() == 0f) {
                if (cashPayment is CashPayment) {
                    openCashDrawer()
                } else {
                    val coupon = coupons.sumOfFloats { it.amount }
                    val printCardReceipt =
                        storeConfig.receiptInfo.printCardReceipt
                    var total = txnSummary.transactionTotalGrandAmount - coupon
                    if (total < 0) {
                        total = 0f
                    }
                    if (printCardReceipt) {
                        printTransactionUseCase(
                            PrintTxnReceiptInput(
                                storeConfig = storeConfig,
                                txnStartTime = saleTxn.txnStartTime,
                                txnEndTime = saleTxn.txnEndTime,
                                txnId = saleTxn.txnId,
                                cartItems = cartItems,
                                coupon = coupon,
                                subTotal =
                                txnSummary
                                    .transactionTotalNetAmount,
                                tax =
                                txnSummary
                                    .transactionTotalTaxNetAmount,
                                total = total,
                                loyaltyAccount = loyaltyId,
                                ebtInfo = ebtInfo,
                                cardPayment =
                                txnPayment[
                                    TxnPaymentType
                                        .Card],
                                cashPayment =
                                txnPayment[
                                    TxnPaymentType
                                        .Cash],
                                ebtPayment =
                                txnPayment[
                                    TxnPaymentType.EBT],
                                chequePayment =
                                txnPayment[
                                    TxnPaymentType
                                        .Cheque],
                                creditPayment =
                                txnPayment[
                                    TxnPaymentType
                                        .Credit],
                                change = txnSummary.change(),
                                appliedFees = saleTxn.appliedFees,
                                lotteryPayout = null,
                                lotteryPayouts =
                                state.payout.lotteryPayouts,
                                dob = dob
                            )
                        )
                    }
                    if (state.payout.lotteryPayouts.isNotEmpty()) {
                        createLotteryPayout(
                            PayoutInput(
                                lotteryPayouts =
                                state.payout.lotteryPayouts,
                                cashierId = cashierId(),
                                fromSale = true,
                                txnStartTime = txn.txnStartTime,
                                storeConfig = _storeConfig.value
                            )
                        )
                    }
                    resetTransactionSwapState(null)
                }
            }
        }

    private fun startPaymentApi(
        cents: Int,
        amount: Float,
        transaction: Transaction,
        attempt: Int,
        token: String,
        cashierId: String,
        epoch: Int,
        storeLevelConfig: StoreUsers,
        appliedFees: List<AppliedFee> = listOf()
    ) =
        viewModelScope.launch {
            paymentRepository
                .securedPaymentProcessor(
                    tData =
                    TIM(
                        amount = Optional.present(cents),
                        txnId = Optional.present(transaction.txnId),
                        txnType = Optional.present("merchandise"),
                        attempt = Optional.present(attempt),
                        token = Optional.present(token),
                    ),
                    uid = cashierId,
                    eventEpoch = epoch,
                    autoRetry = storeLevelConfig.autoRetryPayments
                )
                .stateIn(viewModelScope)
                .collectLatest { result ->
                    when (result) {
                        is Result.Success -> {
                            val paymentResponse = result.data
                            val cardPayment =
                                CardPayment(
                                    amount = amount,
                                    payFacId =
                                    paymentResponse
                                        .payFacInfo
                                        .cardNumber
                                        ?: "",
                                    brand =
                                    paymentResponse
                                        .payFacInfo
                                        .cardBrand
                                        ?: "",
                                    paymentType =
                                    paymentResponse
                                        .payFacInfo
                                        .paymentType
                                        ?: "",
                                )
                            val cardInfo =
                                CardInfo(
                                    merchantId =
                                    paymentResponse
                                        .data
                                        ?.mid
                                        ?: "",
                                    approvalCode =
                                    paymentResponse
                                        .payFacInfo
                                        .approvalCode
                                        ?: "",
                                    logo =
                                    paymentResponse
                                        .payFacInfo
                                        .cardBrand
                                        ?: "",
                                    transactionId =
                                    transaction.txnId,
                                    account =
                                    paymentResponse
                                        .payFacInfo
                                        .cardNumber
                                        ?: "",
                                    entry =
                                    paymentResponse
                                        .payFacInfo
                                        .cardEntryMode
                                        ?: "",
                                    label =
                                    paymentResponse
                                        .payFacInfo
                                        .applicationLabel
                                        ?: "",
                                )
                            _toastMessage.emit(
                                ToastMessage(
                                    paymentResponse
                                        .payFacInfo
                                        .infoMsg
                                        ?: "Payment Successful",
                                    ToastMessageType.Success
                                )
                            )
                            _uiState.update {
                                it.copy(appliedFees = appliedFees)
                            }
                            _isLoading.emit(null)
                            handleCardPaymentSuccess(
                                cardPayment,
                                cardInfo,
                                amount,
                                transaction
                            )
                        }

                        is Result.Error -> {
                            handlePaymentFailure(
                                result.errorMessage,
                                result.errorCode,
                                transaction.txnId,
                                storeLevelConfig.autoRetryPayments,
                                result.retrying,
                                result.retryCount
                            )
                        }

                        is Result.Loading -> {
                            _isLoading.emit(
                                "Payment is being processed..."
                            )
                        }
                    }
                }
        }

    private fun savePendingTransaction(transaction: Transaction) =
        viewModelScope.launch {
            if (transaction is BaseTransaction) {
                val transactionSummary = _uiState.value.transactionSummary
                val txnSummary =
                    getTransactionSummaryUseCase(
                        txnItems.value,
                        transactionSummary.couponAmount,
                        uiState.value.payout.lotteryPayouts,
                        storeLevelConfig.value?.feeConfig,
                        transactionSummary
                    )
                val txn =
                    SaleTransaction(
                        txnEndTime = Date(),
                        txnStartTime = transaction.txnStartTime,
                        txnTotalGrandAmount =
                        txnSummary.transactionTotalGrandAmount,
                        txnTotalGrossAmount =
                        txnSummary.transactionTotalGrossAmount,
                        txnTotalNetAmount =
                        txnSummary.transactionTotalNetAmount,
                        txnTotalTaxNetAmount =
                        txnSummary.transactionTotalTaxNetAmount,
                        cashierId = transaction.cashierId,
                        txnStatus = TransactionStatus.Pending,
                        statusHistory =
                        HashMap(transaction.statusHistory.orEmpty())
                            .apply {
                                put(
                                    Date().epochInSeconds(),
                                    TransactionStatus
                                        .Pending
                                )
                            },
                        txnItems = transaction.txnItems,
                        txnId = transaction.txnId,
                    )
                /*
                                   Note: Important to update _currentTransaction, because DeleteCartItemUseCase uses
                                   it's status to check whether it needs to be deleted.
                                */
                _currentTransaction.emit(txn)
                transactionRepository.saveTransaction(txn)
            }
        }

    fun cancelPaymentStream() =
        viewModelScope.launch {
            paymentProcessor.cancelPayment()
            _uiState.update { ui ->
                ui.copy(paymentResponseWS = null, transactionId = null)
            }
            paymentJob?.cancel()
        }

    fun clearPaymentStreamState() {
        _uiState.update { ui -> ui.copy(paymentResponseWS = null, transactionId = null) }
        paymentJob?.cancel()
    }

    private fun handlePaymentFailure(
        errorMessage: String,
        errorCode: String?,
        txnId: String,
        autoRetryPayment: Boolean?,
        retrying: Boolean,
        retryCount: Int
    ) =
        viewModelScope.launch {
            _isLoading.emit(null)
            _uiState.update {
                it.copy(
                    paymentState =
                    it.paymentState.copy(
                        message =
                        ToastMessage(
                            errorMessage,
                            ToastMessageType.Error
                        ),
                        showPaymentDialog = true,
                        showRetry =
                        (errorCode !=
                                MAX_ATTEMPTS_REACHED &&
                                autoRetryPayment == false),
                        transactionId = txnId,
                        retrying = retrying,
                        retryCount = retryCount
                    )
                )
            }
            (_currentTransaction.value as? SaleTransaction)?.let {
                val txn = it.copy(statusReason = StatusReason.PAYMENT_FAILED)
                _currentTransaction.emit(txn)
                transactionRepository.saveTransaction(txn)
            }
        }

    fun clearMenuSelectedKey() {
        _uiState.update { ui -> ui.copy(selectedMenuKeyId = null) }
    }

    fun clearSelectedDept() {
        _uiState.update { ui -> ui.copy(selectedDeptForDetail = null) }
    }

    fun onDepartmentClick(item: Department) =
        viewModelScope.launch {
            if (!uiState.value.allowAddingItemsToCart) {
                return@launch
            }
            _selectedPLUItem.emit(null)
            _selectedDepartment.emit(item)
            if (isUnderAge(item.minimumCustomerAge, _customerAge.value) &&
                !_enableSkip.value
            ) {
                _uiState.update { it.copy(showAgeVerificationDialog = true) }
                return@launch
            }
            _uiState.emit(
                _uiState.value.copy(numPadResult = NumPadResult.AddDepartmentItem)
            )
        }

    private fun getEpoch(): Int {
        val state = uiState.value
        return if (state.paymentState.paymentEpoch > 0) state.paymentState.paymentEpoch
        else {
            val epochInSeconds = Date().epochInSeconds()
            _uiState.update {
                it.copy(
                    paymentState =
                    it.paymentState.copy(paymentEpoch = epochInSeconds)
                )
            }
            epochInSeconds
        }
    }

    private fun processEbtWithEpx(
        ebtType: EbtType,
        paxTxnType: EpxTransactionType,
        ebtPayAmount: Float?
    ) =
        viewModelScope.launch {
            val storeLevelConfig = _storeLevelConfig.value
            var transaction = transaction()
            if (paxTxnType != EpxTransactionType.INQUIRY) {
                if (transaction is BaseTransaction) {
                    transaction =
                        transaction.copy(
                            txnStatus = TransactionStatus.Pending,
                            statusHistory =
                            HashMap(
                                transaction
                                    .statusHistory
                                    .orEmpty()
                            )
                                .apply {
                                    put(
                                        Date().epochInSeconds(),
                                        TransactionStatus
                                            .Pending
                                    )
                                }
                        )
                }
                _currentTransaction.emit(transaction)
            }
            val cashierId = cashierId()
            val epoch = Date().epochInSeconds()
            val ebtAmount = ebtPayAmount ?: uiState.value.transactionSummary.totalEBT()
            val cents = ebtAmount.toDouble().convertDollarToCentPrecisely().toString()
            val ebtCountType: EbtCountType =
                when (ebtType) {
                    EbtType.FOOD_STAMP -> EbtCountType.FOOD_STAMP
                    else -> EbtCountType.CASH_BENEFITS
                }
            paymentUseCase
                .processEpxEbtRequest(
                    PaxPaymentInput(
                        transaction.txnId,
                        paxPaymentService.getCurrentBatchId(),
                        cents,
                        cashierId,
                        epoch,
                        ebtCountType,
                        paxTxnType
                    ),
                    convertToSaleTransaction(
                        transaction,
                        _uiState.value.transactionSummary
                    )
                )
                .stateIn(viewModelScope)
                .collect { result ->
                    when (result) {
                        is Result.Loading -> {
                            if (paxTxnType == EpxTransactionType.INQUIRY
                            ) {
                                _loadings.update {
                                    it.toMutableSet().apply {
                                        add(
                                            Loadings.EBT_BALANCE
                                        )
                                    }
                                }
                            } else {
                                _isLoading.emit(
                                    "Payment is being processed..."
                                )
                            }
                        }

                        is Result.Error -> {
                            if (paxTxnType == EpxTransactionType.INQUIRY
                            ) {
                                _loadings.update {
                                    it.toMutableSet().apply {
                                        remove(
                                            Loadings.EBT_BALANCE
                                        )
                                    }
                                }
                                showToast(
                                    result.errorMessage,
                                    ToastMessageType.Error
                                )
                            } else {
                                handlePaymentFailure(
                                    result.errorMessage,
                                    result.errorCode,
                                    transaction.txnId,
                                    storeLevelConfig
                                        ?.autoRetryPayments,
                                    false,
                                    0
                                )
                            }
                        }

                        is Result.Success -> {
                            result.data?.let {
                                val balanceInCents =
                                    try {
                                        it.amountInformation
                                            ?.balance2
                                            ?.toInt()
                                            ?: 0
                                    } catch (ex: Exception) {
                                        EventUtils
                                            .recordException(
                                                ex
                                            )
                                        0
                                    }
                                val ebtInfo =
                                    EbtInfo(
                                        merchantId =
                                        it.hostInformation
                                            ?.paymentAccountReferenceId
                                            .orEmpty(),
                                        auth =
                                        it.hostInformation
                                            ?.authorizationCode
                                            .orEmpty(),
                                        ref =
                                        it.hostInformation
                                            ?.hostReferenceNumber
                                            .orEmpty(),
                                        cardNo =
                                        it.accountInformation
                                            ?.account,
                                        cardEntryMode =
                                        it.accountInformation
                                            ?.entryMode
                                            ?.name
                                            .orEmpty(),
                                        balanceInCents =
                                        balanceInCents,
                                        ebtType = ebtType
                                    )
                                if (paxTxnType ==
                                    EpxTransactionType
                                        .INQUIRY
                                ) {
                                    _loadings.update {
                                        it.toMutableSet()
                                            .apply {
                                                remove(
                                                    Loadings.EBT_BALANCE
                                                )
                                            }
                                    }
                                    printEbtBalance(ebtInfo)
                                } else {
                                    _toastMessage.emit(
                                        ToastMessage(
                                            it.responseMessage
                                                ?: "Payment Successful",
                                            ToastMessageType
                                                .Success
                                        )
                                    )
                                    _isLoading.emit(null)
                                    val ebtPayment =
                                        EBTPayment(
                                            ebtAmount
                                                .toFloat(),
                                            payFacId =
                                            it.hostInformation
                                                ?.hostReferenceNumber
                                                .orEmpty(),
                                            epxData =
                                            EpxData(
                                                transactionType =
                                                EpxTransactionType
                                                    .SALE,
                                                amount =
                                                ebtAmount,
                                                it.hostInformation
                                                    ?.authorizationCode
                                                    .orEmpty(),
                                                it.hostInformation
                                                    ?.hostResponseMessage
                                                    .orEmpty(),
                                                it.hostInformation
                                                    ?.hostReferenceNumber
                                                    .orEmpty(),
                                            )
                                        )
                                    val txnWithPaymentRecord =
                                        transactionRepository
                                            .getTransaction(
                                                transaction
                                                    .txnId
                                            )
                                    handleEbtPaymentSuccess(
                                        ebtPayment,
                                        ebtInfo,
                                        ebtAmount.toFloat(),
                                        transaction,
                                        txnWithPaymentRecord
                                    )
                                }
                            }
                        }
                    }
                }
        }

    fun onEBTClick(enteredAmount: Float? = null) =
        viewModelScope.launch {
            EventUtils.logEvent(
                EventUtils.Events.EBT_CLICK,
                mapOf(EventUtils.EventProp.AMOUNT to (enteredAmount ?: 0f))
            )
            if (enteredAmount != null) {
                hideCardPaymentDialog()
            }
            /*
                           Currently we are supporting a single card payment.
                        */
            if (_txnPayment.value[TxnPaymentType.EBT] != null) {
                showToast(
                    "Currently, a single EBT payment is supported",
                    ToastMessageType.Info
                )
                return@launch
            }
            val state = uiState.value
            val storeLevelConfig = _storeLevelConfig.value
            if (!isValidTransaction() ||
                state.transactionSummary.disableEBT() ||
                storeLevelConfig == null
            ) {
                return@launch
            }
            if (storeLevelConfig.ebtPayments &&
                storeLevelConfig.streamPayments &&
                storeLevelConfig.ebtOptions.size == 1
            ) {
                _uiState.emit(
                    _uiState.value.copy(
                        selectedEbtType =
                        storeLevelConfig.ebtOptions.first(),
                        showEBTDialog = true,
                        enteredEbtAmount = enteredAmount,
                        paymentResponseWS = null
                    )
                )
            } else {
                _uiState.update {
                    it.copy(
                        showEBTDialog = true,
                        enteredEbtAmount = enteredAmount,
                        paymentResponseWS = null
                    )
                }
            }
        }

    fun selectEbtType(ebtType: EbtType, finishTransaction: Boolean, payAmount: Float?) {
        _uiState.update { it.copy(selectedEbtType = ebtType) }
        onEBTProceedClick(finishTransaction, payAmount)
    }

    private fun processEbtPayment(ebtType: EbtType, payAmount: Float?) {
        val attempt = uiState.value.paymentState.attempts + 1
        _uiState.update { it.copy(paymentState = it.paymentState.copy(attempts = attempt)) }
        viewModelScope.launch {
            val storeConfig = _storeConfig.value
            val transactionSummary = uiState.value.transactionSummary
            var ebtAmount = payAmount ?: transactionSummary.totalEBT()
            // Check if EBT card balance is available
            val ebtBalanceCents = _uiState.value.ebtBalancesCents[ebtType]
            ebtBalanceCents?.let {
                if (ebtBalanceCents > 0f && (ebtBalanceCents / 100) < ebtAmount) {
                    ebtAmount = ebtBalanceCents / 100
                }
            }
            if (storeConfig == null || ebtAmount <= 0f) {
                return@launch
            }
            val transaction = transaction()
            val token =
                hOtpGenerator.generateHOTP(
                    tid = transaction.txnId,
                    token = storeConfig.token,
                    counter = storeConfig.posNumber.toLong()
                )
            val cents = ebtAmount.toDouble().convertDollarToCentPrecisely()
            val paymentInput =
                PaymentInputDTO(
                    getEpoch(),
                    TData(
                        token,
                        cents,
                        transaction.txnId,
                        attempt,
                        TxnType.merchandise,
                        PaymentType.ebt,
                        0,
                        ebtType
                    )
                )
            savePendingTransaction(transaction)
            startPaymentStream(
                ebtAmount,
                paymentInput,
                storeConfig.posId,
                cashierId(),
                transaction
            )
        }
    }

    fun onNoSaleClick() {
        EventUtils.logEvent(EventUtils.Events.NO_SALE_CLICK)
        viewModelScope.launch {
            if (isValidTransaction()) {
                return@launch
            }
            printerXManager.validateCashDrawer(
                onCashDrawerNotAvailable = {
                    _toastMessage.emit(
                        ToastMessage(
                            message =
                            "Cash drawer not detected, please connect the cash drawer",
                            type = ToastMessageType.Error
                        )
                    )
                },
                onCashDrawerAvailable = {
                    try {
                        it.openAsync()
                        val transaction = transaction()
                        saveTransaction(
                            NoSaleTransaction(
                                txnStartTime =
                                transaction.txnStartTime,
                                cashierId = transaction.cashierId,
                                txnId = transaction.txnId,
                                statusHistory =
                                hashMapOf(
                                    Date().epochInSeconds() to
                                            TransactionStatus
                                                .Complete
                                )
                            )
                        )
                    } catch (e: Exception) {
                        EventUtils.recordException(e)
                    }
                }
            )
        }
    }

    fun onRedeemClick() =
        viewModelScope.launch {
            EventUtils.logEvent(EventUtils.Events.COUPON_CLICK)
            if (!isValidTransaction()) {
                return@launch
            }
            _uiState.update { it.copy(showCouponDialog = true) }
        }

    fun onKeyEvent(event: KeyEvent) =
        viewModelScope.launch {
            val keyCode = event.nativeKeyEvent.keyCode
            if (event.nativeKeyEvent.unicodeChar == 0) {
                return@launch
            }
            // accept only 0-9 and "enter"
            if (event.nativeKeyEvent.action == android.view.KeyEvent.ACTION_DOWN ||
                keyCode == android.view.KeyEvent.KEYCODE_ENTER
            ) {
                if (keyCode == android.view.KeyEvent.KEYCODE_ENTER) {
                    val b = barcode
                    barcode = ""
                    if (b.isNotEmpty()) onBarcodeAvailable(b)
                } else {
                    barcode += event.nativeKeyEvent.unicodeChar.toChar()
                }
            }
        }

    fun dismissGUpcDialog() =
        viewModelScope.launch { _uiState.emit(_uiState.value.copy(gUpcItem = null)) }

    fun dismissBarcodePriceBookList() =
        viewModelScope.launch {
            _uiState.update { it.copy(barcodePriceBookList = emptyList()) }
        }

    private fun onBarcodeAvailable(barcode: String) =
        viewModelScope.launch {
            val storeId = _storeConfig.value?.storeCode ?: ""
            when {
                isValidTransactionId(storeId, barcode) -> handleTxnScan(barcode)
                isDlInput(barcode) || drivingLicenceData.isNotEmpty() -> {
                    val dob = parseDobFromPdf417(barcode)
                    dob?.let {
                        drivingLicenceData = barcode
                        onCustomerAgeSubmit(it.toDate().replace("-", ""))
                        delay(1000L)
                        drivingLicenceData = ""
                    }
                }

                barcode.all { it.isDigit() } -> handleGTIN(barcode)
            }
        }

    private fun handleGTIN(barcode: String) =
        viewModelScope.launch {
            val storeId = _storeConfig.value?.storeCode ?: ""

            var barcodeToSearch = barcode
            try {
                barcodeToSearch = barcode.convertToEAN()
            } catch (e: Exception) {
                eventLogger.barcodeScan(barcode, "could not convert")
                EventUtils.recordException(e)
                _uiState.update {
                    it.copy(
                        errorDialogMessage =
                        "Could not convert this barcode, please share this barcode : $barcode with Customer Support to fix it"
                    )
                }

                return@launch
            }

            val pluItem = priceBookRepository.findByBarcode(barcodeToSearch, storeId)

            if (pluItem.isEmpty()) {
                val gUPCItem = gUPCRepository.findByBarcode(barcodeToSearch)

                if (gUPCItem != null) {
                    _uiState.update {
                        it.copy(gUpcItem = GUPCItem(pluItem = gUPCItem))
                    }
                } else {
                    gUPCCorrectionRepository.reportMissingItem(
                        ReportMissingItem(
                            upc = barcodeToSearch,
                            firstRequested = Date()
                        )
                    )
                    _uiState.update {
                        it.copy(
                            gUpcItem =
                            GUPCItem(
                                pluItem =
                                PluItem(
                                    pluModifier =
                                    "000",
                                    pluId =
                                    barcodeToSearch,
                                    description =
                                    "",
                                    pluFormat =
                                    PluFormatType
                                        .Ean14,
                                ),
                                showReport = false
                            )
                        )
                    }
                }
                return@launch
            }

            if (_uiState.value.isPriceCheck) {
                _uiState.emit(uiState.value.copy(priceCheckItem = pluItem[0]))
                return@launch
            }

            if (pluItem.size > 1) {
                _uiState.update { it.copy(barcodePriceBookList = pluItem) }
                return@launch
            }

            if (_uiState.value.transactionSummary.totalAmountCollected() <= 0) {
                onPriceBookItemClick(pluItem[0])
            }
        }

    private fun handleTxnScan(txnId: String) {
        _transactionScanned.tryEmit(txnId)
    }

    fun showSaveDialog() {
        _uiState.update { it.copy(showSaveDialog = true) }
    }

    fun hideSaveDialog() {
        _uiState.update { it.copy(showSaveDialog = false) }
    }

    fun showVendorDialog() {
        _uiState.update { it.copy(showVendorDialog = true) }
    }

    fun hideVendorDialog() {
        _uiState.update { it.copy(showVendorDialog = false) }
    }

    fun onSaveClick(label: String) =
        viewModelScope.launch {
            EventUtils.logEvent(EventUtils.Events.SAVE_CLICK)
            if (!isValidTransaction()) {
                return@launch
            }

            val transaction = transaction()
            val state = uiState.value

            val loyaltyId = state.loyaltyState.loyaltyId

            val pumpMetas =
                txnItems.value.filterIsInstance<TransactionItemWithFuel>().map {
                    PumpMeta(it.preFuel.deviceId)
                }

            val fuelMeta = if (pumpMetas.isNotEmpty()) FuelMeta(pumpMetas) else null

            saveTransaction(
                SaleTransaction(
                    txnEndTime = Date(),
                    txnTotalGrandAmount =
                    state.transactionSummary
                        .transactionTotalGrandAmount,
                    txnTotalGrossAmount =
                    state.transactionSummary
                        .transactionTotalGrossAmount,
                    txnTotalNetAmount =
                    state.transactionSummary.transactionTotalNetAmount,
                    txnTotalTaxNetAmount =
                    state.transactionSummary
                        .transactionTotalTaxNetAmount,
                    dob = _customerAge.value,
                    cashierId = transaction.cashierId,
                    txnStartTime = transaction.txnStartTime,
                    txnId = transaction.txnId,
                    txnStatus = TransactionStatus.Pending,
                    statusHistory =
                    HashMap(transaction.statusHistory.orEmpty()).apply {
                        put(
                            Date().epochInSeconds(),
                            TransactionStatus.Pending
                        )
                    },
                    txnItems = txnItems.value,
                    txnPayment = _txnPayment.value,
                    txnDiscount = TxnDiscount(state.coupons),
                    lotteryPayouts = state.payout.lotteryPayouts,
                    accountInfo =
                    if (loyaltyId == null) null
                    else LoyaltyAccountInfo(loyaltyId = loyaltyId),
                    fuelMeta = fuelMeta,
                    txnLabel = label
                )
            )
        }

    fun dismissErrorDialog() =
        viewModelScope.launch { _uiState.update { it.copy(errorDialogMessage = null) } }

    fun dismissVoidConfirmation() =
        viewModelScope.launch { _uiState.update { it.copy(showVoidConfirmation = false) } }

    fun setIdleImageResource(file: File?) {
        idleResourceFile = file
        if (_paxTerminalState.value is PaxConnectionResult.ConnectionSuccess) {
            setPinpadIdleImage()
        }
    }

    private fun setPinpadIdleImage() =
        viewModelScope.launch {
            if (!_storeConfig.value?.pinpadIdleImageSet.isTrue()) {
                idleResourceFile?.let {
                    val req =
                        UpdateResourceFileRequest.Builder()
                            .fileUrl(it.absolutePath)
                            .fileType(FileType.RESOURCE_FILE)
                            .build()
                    when (val res = paxPaymentService.setIdleImage(req)) {
                        is Result.Error ->
                            EventUtils.recordException(
                                Exception(
                                    "Failed to set idle image error: ${res.errorMessage}, code: ${res.errorCode}"
                                )
                            )

                        is Result.Success -> {
                            val posId =
                                DeviceIdentifier.getSerialNumber()
                                    ?: return@launch
                            storeRepository.setPinpadIdleImageSet(posId)
                        }

                        else -> {}
                    }
                }
            }
        }

    fun onVoidClick() =
        viewModelScope.launch {
            /** There could be only deleted items, so we can't use isValidTransaction */
            if (txnItems.value.isEmpty() &&
                uiState.value.payout.lotteryPayouts.isEmpty()
            ) {
                return@launch
            }
            EventUtils.logEvent(EventUtils.Events.VOID_CLICK)
            val state = uiState.value

            val transaction = transaction()

            val transactionSummary = state.transactionSummary
            /**
             * If EBT amount collected is > 0, delete unpaid items Note: Currently only
             * handling for partial EBT payments i.e. total amount to pay is more than
             * what is paid through EBT. And at the same time expecting consumers to pay
             * full EBT amount i.e. pay for all the EBT items in the cart.
             */
            if (transactionSummary.ebtAmountCollected > 0f && ebtInfo != null) {
                var unallocatedAmount =
                    (transactionSummary.ebtAmountCollected +
                            transactionSummary.ebtPromotion +
                            transactionSummary.ebtLoyalty)
                txnItems.value.forEach {
                    if (it.isEBT() && it.status == TransactionItemStatus.Normal
                    ) {
                        if (unallocatedAmount + 0.001 >= it.totalItemPrice()
                        ) {
                            unallocatedAmount -= it.totalItemPrice()
                        } else {
                            it.status = TransactionItemStatus.Deleted
                        }
                    } else {
                        it.status = TransactionItemStatus.Deleted
                    }
                }
                dashboardStateProvider.updateTxnItems(txnItems.value)
                val txnSummary =
                    getTransactionSummaryUseCase(
                        txnItems.value,
                        transactionSummary.couponAmount,
                        uiState.value.payout.lotteryPayouts,
                        storeLevelConfig.value?.feeConfig,
                        transactionSummary
                    )
                val cashPayment = _txnPayment.value[TxnPaymentType.Cash]
                if (cashPayment is CashPayment) {
                    _txnPayment.emit(
                        _txnPayment.value.apply {
                            remove(TxnPaymentType.Cash)
                        }
                    )
                }
                _uiState.update {
                    it.copy(
                        transactionSummary = txnSummary,
                        cashPaymentState =
                        it.cashPaymentState.copy(completed = true),
                        cardPaymentState =
                        it.cardPaymentState.copy(completed = true),
                        ebtPaymentState =
                        it.ebtPaymentState.copy(completed = true),
                        checkPaymentState =
                        it.checkPaymentState.copy(completed = true),
                        creditPaymentState =
                        it.creditPaymentState.copy(completed = true)
                    )
                }
                return@launch
            }

            val voidTxn =
                VoidTransaction(
                    txnStartTime = transaction.txnStartTime,
                    txnEndTime = Date(),
                    cashierId = transaction.cashierId,
                    txnStatus = TransactionStatus.Complete,
                    statusHistory =
                    HashMap(transaction.statusHistory.orEmpty()).apply {
                        put(
                            Date().epochInSeconds(),
                            TransactionStatus.Complete
                        )
                    },
                    txnItems = txnItems.value,
                    txnId = transaction.txnId,
                    txnTotalGrandAmount =
                    state.transactionSummary
                        .transactionTotalGrandAmount,
                    txnTotalGrossAmount =
                    state.transactionSummary
                        .transactionTotalGrossAmount,
                    txnTotalNetAmount =
                    state.transactionSummary.transactionTotalNetAmount,
                    txnTotalTaxNetAmount =
                    state.transactionSummary
                        .transactionTotalTaxNetAmount
                )
            saveTransaction(voidTxn)
            printTransactionUseCase.printIfAllowed(_storeConfig.value, voidTxn)
        }

    fun showRefundDialog() =
        viewModelScope.launch {
            if (txnItems.value.isEmpty() ||
                uiState.value.payout.lotteryPayouts.isNotEmpty()
            ) {
                return@launch
            }
            _uiState.update { it.copy(showRefundDialog = true) }
        }

    fun hideRefundDialog() {
        _uiState.update { it.copy(showRefundDialog = false) }
    }

    fun processRefund(selectedMop: String, refundReason: String) =
        viewModelScope.launch {
            if (_loadings.value.contains(Loadings.REFUNDING)) {
                return@launch
            }
            _loadings.update { it.toMutableSet().apply { add(Loadings.REFUNDING) } }
            val state = uiState.value
            var saleTxn =
                convertToSaleTransaction(transaction(), state.transactionSummary)
            val totalRefundableAmount =
                state.transactionSummary.transactionTotalGrandAmount
            when (selectedMop) {
                TxnPaymentType.Cash.type -> {
                    cashDrawerOpenCheck(
                        onOpenCallback = {
                            saleTxn =
                                saleTxn.copy(
                                    txnPayment =
                                    mapOf(
                                        TxnPaymentType
                                            .Cash to
                                                CashPayment(
                                                    tender =
                                                    totalRefundableAmount
                                                )
                                    )
                                )
                            refundTxn =
                                saleTxn.convertToRefundTransaction(
                                    refundReason,
                                    ""
                                )
                            _uiState.update {
                                it.copy(
                                    refundState =
                                    RefundState(
                                        Date(),
                                        totalRefundableAmount,
                                        null
                                    )
                                )
                            }
                            _loadings.update {
                                it.toMutableSet().apply {
                                    remove(Loadings.REFUNDING)
                                }
                            }
                        },
                        onFailureCallback = {
                            _loadings.update {
                                it.toMutableSet().apply {
                                    remove(Loadings.REFUNDING)
                                }
                            }
                        }
                    )
                }

                TxnPaymentType.Card.type,
                EbtType.FOOD_STAMP.value,
                EbtType.CASH_BENEFIT.value -> {
                    val ebtType =
                        when (selectedMop) {
                            EbtType.FOOD_STAMP.value ->
                                EbtCountType.FOOD_STAMP

                            EbtType.CASH_BENEFIT.value ->
                                EbtCountType.CASH_BENEFITS

                            else -> null
                        }
                    val cents =
                        totalRefundableAmount
                            .toDouble()
                            .convertDollarToCentPrecisely()
                            .toString()
                    val request =
                        if (ebtType != null) {
                            paymentUseCase.processEpxEbtRequest(
                                PaxPaymentInput(
                                    saleTxn.txnId,
                                    paxPaymentService
                                        .getCurrentBatchId(),
                                    cents,
                                    cashierId(),
                                    Date().epochInSeconds(),
                                    ebtType,
                                    EpxTransactionType.RETURN
                                ),
                                saleTxn
                            )
                        } else {
                            paymentUseCase.processEpxCreditRequest(
                                PaxPaymentInput(
                                    saleTxn.txnId,
                                    paxPaymentService
                                        .getCurrentBatchId(),
                                    cents,
                                    cashierId(),
                                    Date().epochInSeconds(),
                                    null,
                                    EpxTransactionType.RETURN
                                ),
                                saleTxn
                            )
                        }
                    request.stateIn(viewModelScope).collectLatest { result ->
                        when (result) {
                            is Result.Loading -> {
                                _loadings.update {
                                    it.toMutableSet().apply {
                                        add(
                                            Loadings.REFUNDING
                                        )
                                    }
                                }
                            }

                            is Result.Error -> {
                                _loadings.update {
                                    it.toMutableSet().apply {
                                        remove(
                                            Loadings.REFUNDING
                                        )
                                    }
                                }
                                showToast(
                                    ToastMessage(
                                        message =
                                        result.errorMessage,
                                        type =
                                        ToastMessageType
                                            .Error
                                    )
                                )
                            }

                            is Result.Success -> {
                                result.data?.let {
                                    val epxData =
                                        EpxData(
                                            transactionType =
                                            EpxTransactionType
                                                .RETURN,
                                            amount =
                                            totalRefundableAmount,
                                            authorizationCode =
                                            it.hostInformation
                                                ?.authorizationCode,
                                            hostResponseMessage =
                                            it.hostInformation
                                                ?.hostResponseMessage,
                                            hostResponseNumber =
                                            it.hostInformation
                                                ?.hostReferenceNumber
                                        )
                                    val cardPayment =
                                        CardPayment(
                                            amount =
                                            totalRefundableAmount,
                                            payFacId =
                                            it.hostInformation
                                                ?.hostReferenceNumber
                                                .orEmpty(),
                                            brand =
                                            it.accountInformation
                                                ?.cardType
                                                ?.name
                                                .orEmpty(),
                                            paymentType =
                                            it.paymentEmvTag
                                                ?.appLabel
                                                .orEmpty(),
                                            epxData =
                                            epxData
                                        )
                                    saleTxn =
                                        saleTxn.copy(
                                            txnPayment =
                                            mapOf(
                                                TxnPaymentType
                                                    .Card to
                                                        cardPayment
                                            )
                                        )
                                    saleTxn.convertToRefundTransaction(
                                        refundReason,
                                        ""
                                    )
                                        .let {
                                            val refundTxn =
                                                it.copy(
                                                    refundInfo =
                                                    it.refundInfo
                                                        .copy(
                                                            epxData =
                                                            if (ebtType !=
                                                                null
                                                            )
                                                                null
                                                            else
                                                                epxData,
                                                            epxEbtData =
                                                            if (ebtType !=
                                                                null
                                                            )
                                                                epxData
                                                            else
                                                                null
                                                        )
                                                )
                                            transactionRepository
                                                .completeRefundTransaction(
                                                    refundTxn
                                                )
                                            resetTransactionSwapState(
                                                null
                                            )
                                        }
                                    showToast(
                                        ToastMessage(
                                            message =
                                            "${(totalRefundableAmount).toDollars()} refunded successfully",
                                            type =
                                            ToastMessageType
                                                .Success
                                        )
                                    )
                                }
                                _loadings.update {
                                    it.toMutableSet().apply {
                                        remove(
                                            Loadings.REFUNDING
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

    fun completeRefundTxn() =
        viewModelScope.launch {
            refundTxn?.let {
                transactionRepository.completeRefundTransaction(it)
                refundTxn = null
                resetTransactionSwapState(null)
            }
        }

    fun numberPadHelper(type: NumPadResult? = null) =
        viewModelScope.launch { _uiState.update { it.copy(numPadResult = type) } }

    fun onCashClick() =
        viewModelScope.launch {
            EventUtils.logEvent(EventUtils.Events.CASH_CLICK)
            if (!isValidTransaction()) {
                return@launch
            }

            if (uiState.value.transactionSummary.transactionTotalGrandAmount < 0) {
                _uiState.emit(
                    completeCashTransaction(uiState.value.transactionSummary)
                )
            } else {
                _uiState.update {
                    it.copy(numPadResult = NumPadResult.CashTransaction)
                }
            }
        }

    fun onCheckClick() =
        viewModelScope.launch {
            EventUtils.logEvent(EventUtils.Events.CHECK_CLICK)
            if (!isValidTransaction()) {
                return@launch
            }
            _uiState.update { it.copy(showCheckDialog = true) }
        }

    fun dismissCheckDialog() =
        viewModelScope.launch { _uiState.update { it.copy(showCheckDialog = false) } }

    fun onPriceCheckClick() =
        viewModelScope.launch { _uiState.update { it.copy(isPriceCheck = true) } }

    fun onPriceCheckDismiss() =
        viewModelScope.launch {
            _uiState.update { it.copy(isPriceCheck = false, priceCheckItem = null) }
        }

    private fun handleCashDrawerAndCompleteTransaction() =
        viewModelScope.launch {
            val uiStateRef = uiState.value
            val transactionSummary = uiStateRef.transactionSummary
            val txnPayment = _txnPayment.value

            val pendingAmount = transactionSummary.pendingAmount()
            val amountCollected = transactionSummary.cashAmountCollected

            txnPayment[TxnPaymentType.Cash] =
                CashPayment(
                    tender = amountCollected,
                    change =
                    if (transactionSummary.change() < 0) 0f
                    else transactionSummary.change()
                )
            _txnPayment.emit(txnPayment)

            _uiState.update { state ->
                state.copy(
                    cashPaymentState =
                    state.cashPaymentState.copy(
                        amount = amountCollected,
                        completed = false
                    ),
                    transactionSummary = transactionSummary
                )
            }

            if (amountCollected > 0 || pendingAmount < 0f) {
                // open the cash drawer to keep it in the drawer

                printerXManager.validateCashDrawer(
                    onCashDrawerNotAvailable = {
                        _toastMessage.emit(
                            ToastMessage(
                                message =
                                "Cash drawer not detected, please connect the cash drawer",
                                type = ToastMessageType.Error
                            )
                        )
                    },
                    onCashDrawerAvailable = {
                        try {
                            if (!it.isOpen && it.openAsync()) {
                                _uiState.emit(
                                    completeCashTransaction(
                                        transactionSummary
                                    )
                                )
                            }
                        } catch (e: Exception) {
                            EventUtils.recordException(e)
                        }
                    }
                )
            } else {
                completeCashTransaction()
            }
        }

    private fun openCashDrawer() =
        viewModelScope.launch {
            printerXManager.validateCashDrawer(
                onCashDrawerNotAvailable = {
                    _toastMessage.emit(
                        ToastMessage(
                            message =
                            "Cash drawer not detected, please connect the cash drawer",
                            type = ToastMessageType.Error
                        )
                    )
                },
                onCashDrawerAvailable = {
                    try {
                        if (!it.isOpen) {
                            _uiState.update { state ->
                                state.copy(
                                    requestToCloseCashDrawer =
                                    true
                                )
                            }
                            it.openAsync()
                        }
                    } catch (e: Exception) {
                        EventUtils.recordException(e)
                    }
                }
            )
        }

    fun onCashAmountClick(amount: Float) =
        viewModelScope.launch {
            if (!isValidTransaction()) {
                return@launch
            }

            val uiStateRef = uiState.value
            var transactionSummary = uiStateRef.transactionSummary
            val txnPayment = _txnPayment.value

            val pendingAmount = transactionSummary.pendingAmount()

            if (pendingAmount <= 0f) {
                // open the cash drawer only if the total amount is greater than 0
                handleCashDrawerAndCompleteTransaction()
            } else {
                // collect the cash amount and update the amountCollected
                val collectedAmount =
                    transactionSummary.cashAmountCollected + amount

                transactionSummary =
                    transactionSummary.copy(
                        cashAmountCollected = collectedAmount,
                    )

                txnPayment[TxnPaymentType.Cash] =
                    CashPayment(
                        tender = collectedAmount,
                        change =
                        if (transactionSummary.change() < 0) 0f
                        else transactionSummary.change()
                    )

                _txnPayment.emit(txnPayment)
                _uiState.emit(
                    uiStateRef.copy(
                        transactionSummary = transactionSummary,
                        cashPaymentState =
                        uiStateRef.cashPaymentState.copy(
                            amount = collectedAmount,
                            completed = false
                        )
                    )
                )

                if (transactionSummary.pendingAmount() <= 0f) {
                    delay(500)
                    handleCashDrawerAndCompleteTransaction()
                }
            }
        }

    fun onCheckAmountClick(number: String, amount: Float) =
        viewModelScope.launch {
            if (!isValidTransaction()) {
                return@launch
            }
            var uiStateRef = uiState.value
            var transactionSummary = uiStateRef.transactionSummary
            val txnPayment = _txnPayment.value
            val collectedAmount = transactionSummary.checkAmountCollected + amount
            transactionSummary =
                transactionSummary.copy(checkAmountCollected = collectedAmount)
            txnPayment[TxnPaymentType.Cheque] =
                ChequePayment(amount = collectedAmount, number = number)
            _txnPayment.emit(txnPayment)
            _uiState.update { it.copy(transactionSummary = transactionSummary) }
            if (transactionSummary.pendingAmount() <= 0f) {
                /*
                                   If the pending amount is negative, complete this transaction.
                                   If given cheque amount > pendingAmount, refund through cash, so need to update "change"
                                   in cashPayment.
                                */
                if (transactionSummary.change() > 0) {
                    val cashTxnPayment: CashPayment =
                        (txnPayment[TxnPaymentType.Cash] as? CashPayment)
                            ?: CashPayment()
                    txnPayment[TxnPaymentType.Cash] =
                        cashTxnPayment.copy(
                            change = transactionSummary.change()
                        )
                    _txnPayment.emit(txnPayment)
                    _uiState.update {
                        uiStateRef.copy(
                            checkPaymentState =
                            uiStateRef.checkPaymentState.copy(
                                amount = amount,
                                completed = false,
                                received = true
                            )
                        )
                    }
                    delay(500)
                    // Open the cash drawer to allow cashier to return the
                    // change
                    cashDrawerOpenCheck({
                        _uiState.update {
                            completeCashTransaction(transactionSummary)
                        }
                    })
                } else {
                    if (uiStateRef.cardPaymentState.amount > 0f) {
                        uiStateRef =
                            uiStateRef.copy(
                                cardPaymentState =
                                uiStateRef.cardPaymentState
                                    .copy(
                                        completed =
                                        true
                                    )
                            )
                    }
                    if (uiStateRef.cashPaymentState.amount > 0f) {
                        uiStateRef =
                            uiStateRef.copy(
                                cashPaymentState =
                                uiStateRef.cashPaymentState
                                    .copy(
                                        completed =
                                        true
                                    )
                            )
                    }
                    _uiState.update {
                        uiStateRef.copy(
                            checkPaymentState =
                            uiStateRef.checkPaymentState.copy(
                                amount = amount,
                                completed = true,
                                received = true
                            )
                        )
                    }
                    cashDrawerOpenCheck({})
                    showToast(
                        "Txn saved successfully",
                        ToastMessageType.Success
                    )
                }
            }
        }

    private fun completeCashTransaction(
        transactionSummary: TransactionSummary
    ): DashboardState {
        var uiStateRef = uiState.value
        if (uiStateRef.cardPaymentState.amount > 0f) {
            uiStateRef =
                uiStateRef.copy(
                    cardPaymentState =
                    uiStateRef.cardPaymentState.copy(completed = true)
                )
        }
        return uiStateRef.copy(
            requestToCloseCashDrawer = true,
            transactionSummary = transactionSummary
        )
    }

    fun onLogOutClick(skipBatchClose: Boolean = false): Job =
        viewModelScope.launch {
            if (uiState.value.cartItems.isNotEmpty()) {
                _toastMessage.emit(
                    ToastMessage(
                        message =
                        "Please complete the transaction before logging out"
                    )
                )
                return@launch
            }

            if (ENABLE_EMULATOR) {
                _cashDrawerOpenTime.emit(Date())
                _uiState.update { state ->
                    state.copy(numPadResult = NumPadResult.CashCountInDrawer)
                }
                return@launch
            }
            /**
             * Check if user has permission to close the batch, if yes, check if
             * batch_close_on_logout is enabled. Show a confirmation dialog and then
             * close the batch.
             */
            val userCanCloseBatch =
                _userSettings.value?.canCloseDay.isTrue() ||
                        currentUser.value?.isOwner().isTrue()
            val closeOnLogout =
                _storeConfig.value?.pinpadConfig?.batchCloseOnLogout.isTrue()
            val autoCloseOnLogout =
                _storeConfig.value?.pinpadConfig?.autoBatchCloseOnLogout.isTrue()
            if (userCanCloseBatch && autoCloseOnLogout && !skipBatchClose) {
                closeDay(true)
                return@launch
            }
            if (userCanCloseBatch && closeOnLogout && !skipBatchClose) {
                _uiState.update { it.copy(showBatchCloseConfirmation = true) }
                return@launch
            }

            printerXManager.validateCashDrawer(
                onCashDrawerNotAvailable = {
                    _toastMessage.emit(
                        ToastMessage(
                            message =
                            "Cash drawer is not available, Please connect to login"
                        )
                    )
                },
                onCashDrawerAvailable = {
                    if (!it.isOpen) {
                        it.openAsync()
                    }
                    _cashDrawerOpenTime.emit(Date())

                    _uiState.update { state ->
                        state.copy(
                            numPadResult =
                            NumPadResult.CashCountInDrawer
                        )
                    }
                }
            )
        }

    fun hideBatchCloseConfirmation() {
        _uiState.update { it.copy(showBatchCloseConfirmation = false) }
    }

    private suspend fun resetTransactionSwapState(transaction: Transaction?) {
        _currentTransaction.emit(transaction)
        dashboardStateProvider.updateTxnItems(transaction?.txnItems ?: emptyList())
        _selectedPLUItem.emit(null)
        _selectedDepartment.emit(null)
        _selectedTransactionItem.emit(null)
        _cashDrawerOpenTime.emit(null)
        _currentCouponCount.emit(1)
        _logOutEvent.emit(false)

        _cardInfo.emit(null)
        ebtInfo = null
        val paymentResponseWS = _uiState.value.paymentResponseWS

        if (transaction != null && transaction is SaleTransaction) {
            _customerAge.emit(transaction.dob)

            // handle the case when card payment is not captured
            val txnPayments = transaction.txnPayment.toMutableMap()
            val cardPayment = (txnPayments[TxnPaymentType.Card] as? CardPayment)
            if (!cardPayment.isEpxCaptured()) {
                txnPayments.remove(TxnPaymentType.Card)
            }
            if (cardPayment.isEpxCaptured()) {
                transaction.cardInfo?.let { _cardInfo.emit(it) }
            }
            _txnPayment.emit(txnPayments)
            _uiState.emit(
                DashboardState(
                    loyaltyState =
                    LoyaltyState(
                        loyaltyId =
                        transaction.accountInfo?.loyaltyId
                    ),
                    coupons = transaction.txnDiscount?.coupon ?: emptyList(),
                    payout =
                    PayoutState(
                        lotteryPayouts = transaction.lotteryPayouts
                            ?: emptyList()
                    ),
                    paymentResponseWS = paymentResponseWS,
                    transactionSummary =
                    updateTransactionCollectedAmount.invoke(
                        TransactionSummary(),
                        transaction.txnPayment
                    )
                )
            )
        } else {
            _customerAge.emit(null)
            if (_storeLevelConfig.value?.confirmOnSuccessfulPayment.isTrue()) {
                _uiState.emit(DashboardState(paymentResponseWS = paymentResponseWS))
            } else {
                _uiState.emit(DashboardState())
            }
            _txnPayment.emit(mutableMapOf())
        }

        _enableSkip.emit(false)
    }

    fun onPriceBookItemClick(item: PluItem, addingLinkedItem: Boolean = false) =
        viewModelScope.launch {
            if (!uiState.value.allowAddingItemsToCart) {
                return@launch
            }
            _selectedDepartment.emit(null)
            _selectedPLUItem.emit(item)

            val storeConfig = _storeConfig.value ?: return@launch

            if (txnItems.value.isEmpty()) {
                // create base transaction
                transaction()
            }

            if (isUnderAge(item.minimumCustomerAge, _customerAge.value) &&
                !_enableSkip.value
            ) {
                _uiState.update { it.copy(showAgeVerificationDialog = true) }
                return@launch
            }

            val allItems = txnItems.value

            val itemLine =
                allItems.filterIsInstance<TransactionItemWithPLUItem>().find {
                    it.status == TransactionItemStatus.Normal &&
                            it.pluItem.pluId == item.pluId &&
                            it.pluItem.pluModifier == item.pluModifier
                }

            val updateInput =
                if (itemLine == null) {
                    PriceBookAdd(
                        pluItem = item,
                        cartItems = allItems,
                        storeCode = storeConfig.storeCode,
                        transactionSummary =
                        uiState.value.transactionSummary,
                        taxes = taxMap
                    )
                } else {
                    PriceBookIncrement(
                        cartItems = allItems,
                        storeCode = storeConfig.storeCode,
                        itemLine = itemLine,
                        transactionSummary =
                        uiState.value.transactionSummary,
                        taxes = taxMap
                    )
                }
            val updatePbTxnItemResult = updatePriceBookTxnItemsUseCase(updateInput)
            _appliedPromotions.emit(updatePbTxnItemResult.appliedPromos)
            dashboardStateProvider.updateTxnItems(updatePbTxnItemResult.txnItems)
            reCalculateLoyalty()
            if (!addingLinkedItem) {
                launchAddLinkedPlus(storeConfig.storeCode, fromPluItem = item)
            }
        }

    private fun launchAddLinkedPlus(
        storeCode: String,
        fromPluItem: PluItem? = null,
        fromDepartment: Department? = null
    ) {
        viewModelScope.launch {
            fromPluItem?.linkedPluData?.let { addLinkedPlus(it, storeCode) }
            fromDepartment?.linkedPluData?.let { addLinkedPlus(it, storeCode) }
        }
    }

    private suspend fun addLinkedPlus(linkedPluData: List<LinkedPluData>, storeCode: String) {
        if (linkedPluData.isNotEmpty()) {
            val docIds =
                linkedPluData.map { "$storeCode-${it.pluId}-${it.pluModifier}" }
            val linkedPluItems = priceBookRepository.getPriceBooks(storeCode, docIds)
            val allRequiredItems = linkedPluData.all { it.required }
            if (allRequiredItems) {
                addSelectedPlus(linkedPluItems)
            } else {
                _uiState.update {
                    it.copy(
                        linkedPluData = linkedPluData,
                        linkedPluItems = linkedPluItems
                    )
                }
            }
        }
    }

    fun addSelectedPlus(plus: List<PluItem>) =
        viewModelScope.launch {
            plus.forEach {
                delay(500)
                onPriceBookItemClick(it, true)
            }
            clearLinkedPlus()
        }

    fun clearLinkedPlus() {
        _uiState.update { it.copy(linkedPluItems = emptyList()) }
    }

    fun onAddGupcItemClick(item: PluItem) =
        viewModelScope.launch {
            priceBookRepository.upsertPriceBook(
                item,
                _storeConfig.value?.storeCode ?: ""
            )
        }

    fun onCartItemEdit(it: TransactionItem) =
        viewModelScope.launch {
            EventUtils.logEvent(
                EventUtils.Events.CART_ITEM_EDIT,
                mapOf(EventUtils.EventProp.ITEM_NAME to it.itemName())
            )
            _selectedTransactionItem.emit(it)
            _uiState.update { it.copy(numPadResult = NumPadResult.EditPluQuantity) }
        }

    fun onRecallClick(navController: NavController) =
        viewModelScope.launch {
            if (uiState.value.cartItems.isEmpty()) {
                navController.navigate(Routes.Recall.route)
            } else {
                _toastMessage.emit(
                    ToastMessage("Please complete the current transaction")
                )
            }
        }

    fun recallTransaction(transaction: Transaction) =
        viewModelScope.launch {
            if (txnItems.value.isEmpty()) {
                resetTransactionSwapState(transaction)
            }
        }

    fun onCartItemDelete(item: TransactionItem) =
        viewModelScope.launch {
            val storeConfig = _storeConfig.value ?: return@launch
            val transactionSummary = uiState.value.transactionSummary
            EventUtils.logEvent(
                EventUtils.Events.CART_ITEM_DELETE,
                mapOf(EventUtils.EventProp.ITEM_NAME to item.itemName())
            )
            val deleteCartItemResult =
                deleteCartItemUseCase(
                    DeleteCartItemInput(
                        txnItems = txnItems.value,
                        item = item,
                        currentTransaction = _currentTransaction.value
                    )
                )
            val updatePbTxnItemResult =
                updatePriceBookTxnItemsUseCase(
                    JustUpdateItemLines(
                        cartItems = deleteCartItemResult.txnItems,
                        transactionSummary = transactionSummary,
                        storeCode = storeConfig.storeCode,
                        taxes = taxMap
                    )
                )
            _appliedPromotions.emit(updatePbTxnItemResult.appliedPromos)
            dashboardStateProvider.updateTxnItems(updatePbTxnItemResult.txnItems)
            reCalculateLoyalty()
            if (deleteCartItemResult.clearCart) {
                _uiState.update { it.copy(showVoidConfirmation = true) }
            }
        }

    private fun reCalculateLoyalty() =
        viewModelScope.launch {
            val loyaltyId = uiState.value.loyaltyState.loyaltyId
            if (loyaltyId != null) {
                delay(500)
                onLoyaltyClick()
            }
        }

    fun onLoyaltyClick() =
        viewModelScope.launch {
            EventUtils.logEvent(EventUtils.Events.LOYALTY_CLICK)
            if (!isValidTransaction()) {
                return@launch
            }
            val state = _uiState.value

            val loyaltyId = state.loyaltyState.loyaltyId
            if (loyaltyId == null) {
                when (_storeLevelConfig.value?.loyaltyInput) {
                    LoyaltyInput.CASHIER ->
                        _uiState.update {
                            it.copy(
                                numPadResult =
                                NumPadResult.LoyaltyNumber
                            )
                        }

                    LoyaltyInput.PINPAD -> requestPhoneInputOnPinpad()
                    else ->
                        _uiState.update {
                            it.copy(
                                showUserPhoneNumberInput = true,
                                loyaltyState =
                                it.loyaltyState.copy(
                                    status =
                                    LoyaltyAccountStatus
                                        .WaitingForInput
                                )
                            )
                        }
                }
                return@launch
            }

            _uiState.update {
                it.copy(
                    showUserPhoneNumberInput = false,
                    loyaltyState =
                    it.loyaltyState.copy(
                        status = LoyaltyAccountStatus.Calculating
                    )
                )
            }

            val transaction = transaction()

            val storeCode = _storeConfig.value?.storeCode ?: ""
            _isLoading.emit("Fetching loyalty rewards...")
            loyaltyRepository.getLoyaltyRewards(
                storeCode = storeCode,
                transaction = transaction,
                loyaltyAccountId = loyaltyId,
            )
                .let { result ->
                    when (result) {
                        is Result.Success -> {
                            val rewards = result.data.rewards
                            val discountsByLine =
                                rewards.groupBy {
                                    it.transactionItemId
                                }
                            var existingItems = txnItems.value
                            withContext(ioDispatcher) {
                                existingItems =
                                    existingItems.map { item ->
                                        if (item is
                                                    TransactionItemWithPLUItem
                                        ) {
                                            val loyalty =
                                                if (item.transactionItemId in
                                                    discountsByLine
                                                        .keys
                                                ) {
                                                    discountsByLine[
                                                        item.transactionItemId]
                                                        ?.map { disc
                                                            ->
                                                            ItemDiscount(
                                                                id =
                                                                disc.discount
                                                                    ?.discountId
                                                                    ?: "",
                                                                name =
                                                                disc.discount
                                                                    ?.discountText
                                                                    ?: "",
                                                                amount =
                                                                disc.discount
                                                                    ?.discountAmount
                                                                    ?: 0.0,
                                                                quantity =
                                                                disc.discountQuantity
                                                                    ?: 0,
                                                            )
                                                        }
                                                        ?: emptyList()
                                                } else {
                                                    emptyList()
                                                }

                                            return@map item.copy(
                                                discount =
                                                item.discount
                                                    .copy(
                                                        loyalty =
                                                        loyalty
                                                    )
                                            )
                                        }
                                        item
                                    }
                            }

                            val updatePbTxnItemResult =
                                updatePriceBookTxnItemsUseCase(
                                    JustUpdateItemLines(
                                        cartItems =
                                        existingItems,
                                        storeCode =
                                        storeCode,
                                        transactionSummary =
                                        uiState.value
                                            .transactionSummary,
                                        taxes = taxMap
                                    )
                                )
                            _appliedPromotions.emit(
                                updatePbTxnItemResult.appliedPromos
                            )
                            dashboardStateProvider.updateTxnItems(
                                updatePbTxnItemResult.txnItems
                            )
                            _isLoading.emit(null)
                        }

                        is Result.Error -> {
                            _isLoading.emit(null)

                            _toastMessage.emit(
                                ToastMessage(
                                    result.errorMessage,
                                    ToastMessageType.Error
                                )
                            )
                        }

                        is Result.Loading -> {}
                    }
                }

            _uiState.update {
                it.copy(
                    showUserPhoneNumberInput = false,
                    loyaltyState = it.loyaltyState.copy(status = null)
                )
            }
        }

    private fun requestPhoneInputOnPinpad(): Job =
        viewModelScope.launch {
            val inputTextRequest =
                InputTextRequest.builder()
                    .title(InputTextPrompt.ENTER_PHONE_NUMBER)
                    .inputType(InputType.PHONE_NUMBER)
                    .build()
            _isLoading.emit("Enter phone number on pinpad")

            val timeoutJob = launch {
                delay(20_000) // 20 seconds
                _isLoading.emit(null)
                showToast(
                    "Input timeout - please try again",
                    ToastMessageType.Error
                )
            }

            try {
                when (val res = paxPaymentService.requestTextInput(inputTextRequest)
                ) {
                    is Result.Error -> {
                        timeoutJob.cancel()
                        showToast(
                            "Error ${res.errorMessage}, code: ${res.errorCode}",
                            ToastMessageType.Error
                        )
                        _isLoading.emit(null)
                    }

                    Result.Loading -> {}
                    is Result.Success -> {
                        timeoutJob.cancel()
                        val phoneNo = res.data?.text()
                        if (phoneNo.isNullOrEmpty()) {
                            showToast(
                                "Invalid input received",
                                ToastMessageType.Error
                            )
                        } else {
                            updateLoyaltyAccountId(phoneNo)
                        }
                        _isLoading.emit(null)
                    }
                }
            } catch (e: Exception) {
                timeoutJob.cancel()
                _isLoading.emit(null)
                showToast("Didn't receive any input", ToastMessageType.Error)
            }
        }

    override fun onCleared() {
        super.onCleared()
        timer.cancel()
        pendingFuelListenerJob?.cancel()
    }

    fun onRemoveCouponClick() =
        viewModelScope.launch { _uiState.update { it.copy(coupons = emptyList()) } }

    fun onLotteryRemove(payout: LotteryPayout) =
        viewModelScope.launch {
            _uiState.update {
                val pendingPayouts =
                    it.payout.lotteryPayouts.filter {
                        it.linkedTxnId != payout.linkedTxnId
                    }
                it.copy(
                    payout = it.payout.copy(lotteryPayouts = pendingPayouts),
                    showLotteryDetails = pendingPayouts.isNotEmpty()
                )
            }
        }

    fun onBarcodePriceBookSelect(it: PluItem) =
        viewModelScope.launch { onPriceBookItemClick(it) }

    fun updateLoyaltyAccountId(phoneNumber: String) =
        viewModelScope.launch {
            _uiState.emit(
                _uiState.value.copy(
                    loyaltyState =
                    _uiState.value.loyaltyState.copy(
                        loyaltyId = phoneNumber,
                        status = null
                    )
                )
            )
            delay(50)
            onLoyaltyClick()
        }

    fun onReportClick() =
        viewModelScope.launch {
            if (uiState.value.cartItems.isEmpty()) {
                _uiState.update { it.copy(showReportDialog = true) }
            } else {
                _toastMessage.emit(
                    ToastMessage("Please complete the current transaction")
                )
            }
        }

    fun dismissReportDateDialog() =
        viewModelScope.launch { _uiState.update { it.copy(showReportDialog = false) } }

    fun dismissPaymentRetryDialog() =
        viewModelScope.launch {
            _uiState.update {
                it.copy(
                    paymentState =
                    it.paymentState.copy(showPaymentDialog = false)
                )
            }
        }

    fun dismissInfoDialog() =
        viewModelScope.launch { _uiState.update { it.copy(showInfo = false) } }

    fun updateInfo(appState: BaseAppState?, fqmMessage: FQMMessage?, fdcState: FDCState) =
        viewModelScope.launch {
            if (appState != null) {
                val status = appState.replicatorChange.name
                val pendingCounts = appState.unSyncedTransactions

                _info.update {
                    it.copy(
                        sgStatus =
                        "${appState.replicatorStatus.name} ($status)",
                        pendingCounts = "$pendingCounts",
                        vpnStatus = appState.vpnStatus.name,
                        networkStatus = appState.networkStatus.name,
                    )
                }
            }

            if (fqmMessage != null && fqmMessage.messageType != null) {
                _info.update {
                    it.copy(
                        fqmStatus = fqmMessage.messageType,
                        heartbeatStatus = fdcState.heartbeatStatus
                    )
                }
            }
        }

    fun moveToDashboard(navController: NavController) =
        viewModelScope.launch {
            val storeConfig = _storeConfig.value ?: return@launch

            val drawerAmount = _logoutCashBalance.value

            if (storeConfig.noReportPrintOnLogout != true) {
                _isLoading.emit("Loading Shift Report...")
                val shiftReport =
                    getShiftReportUseCase.get(
                        storeConfig.storeCode,
                        storeConfig.posNumber,
                        drawerAmount
                    )

                shiftReport?.let { report ->
                    val stats = report.stats
                    if (stats.isNotEmpty()) {
                        printEODReportUseCase(
                            EODReportTemplate(
                                stats.first().terminalData
                            )
                        )
                    }
                }
                _isLoading.emit(null)
            }
            _logoutCashBalance.emit(0f)
            val clearUser = userDataSource.clearUser()
            if (clearUser) {
                navController.navigate(Routes.Login.route) {
                    popUpTo(Routes.Dashboard.route) { inclusive = true }
                }
            }
        }

    fun onApplyEBTClick(cartItem: TransactionItem) =
        viewModelScope.launch {
            val storeConfig = _storeConfig.value ?: return@launch
            val state = uiState.value
            val transactionSummary = state.transactionSummary
            val cartItems = txnItems.value

            val updatePbTxnItemResult =
                updatePriceBookTxnItemsUseCase(
                    JustUpdateItemLines(
                        cartItems =
                        cartItems.map { item ->
                            if (item is
                                        TransactionItemWithPLUItem &&
                                item.transactionItemId ==
                                cartItem.transactionItemId
                            ) {
                                return@map item.copy(
                                    pluItem =
                                    item.pluItem
                                        .applyEBT()
                                )
                            }
                            if (item is
                                        TransactionItemWithDepartment &&
                                item.transactionItemId ==
                                cartItem.transactionItemId
                            ) {
                                return@map item.copy(
                                    department =
                                    item.department
                                        .applyEBT()
                                )
                            }
                            item
                        },
                        transactionSummary = transactionSummary,
                        storeCode = storeConfig.storeCode,
                        taxes = taxMap
                    )
                )
            _appliedPromotions.emit(updatePbTxnItemResult.appliedPromos)
            dashboardStateProvider.updateTxnItems(updatePbTxnItemResult.txnItems)
        }

    fun savePluItemsOrder(pluOrder: List<String>) {
        viewModelScope.launch {
            val posId = DeviceIdentifier.getSerialNumber() ?: return@launch
            savePLUItemsOrder(posId, pluOrder)
        }
    }

    fun saveReorderedDepartments(orderedDepartments: List<Department>) {
        viewModelScope.launch {
            _storeConfig.value?.storeCode?.let {
                refDataUseCase.saveReorderedDepartments(
                    it,
                    orderedDepartments,
                    _departmentList.value
                )
            }
        }
    }

    fun saveReorderedMenuKeys(orderedMenuKeys: List<MenuKey>) {
        viewModelScope.launch {
            _storeConfig.value?.storeCode?.let {
                refDataUseCase.saveReorderedMenuKeys(
                    it,
                    orderedMenuKeys,
                    _menuKeys.value
                )
            }
        }
    }

    fun saveReorderedSubMenu(menuKey: MenuKey, subMenu: List<PluItem>) {
        viewModelScope.launch {
            _storeConfig.value?.storeCode?.let {
                refDataUseCase.saveReorderedSubMenu(
                    it,
                    menuKey,
                    subMenu,
                    _menuKeys.value
                )
            }
        }
    }

    fun addNewVendor(vendor: String) {
        viewModelScope.launch {
            _storeConfig.value?.storeCode?.let {
                val saved = refDataUseCase.addVendor(it, vendor)
                if (!saved) {
                    showToast("Failed to save", ToastMessageType.Error)
                }
            }
        }
    }

    fun onMenuKeySelected(item: MenuKey) {
        _uiState.update { it.copy(selectedMenuKeyId = item.id) }
    }

    fun authorizedFuel() {
        viewModelScope.launch { _savedFuelTxn.update { null } }
    }

    fun onDeptDetailSelected(department: Department) =
        viewModelScope.launch {
            _uiState.update { it.copy(selectedDeptForDetail = department) }
            _loadings.update {
                it.toMutableSet().apply { add(Loadings.DEPT_PLU_ITEMS) }
            }
            _deptPluItems.emit(
                priceBookRepository.getPriceBookByDeptId(department.departmentId)
            )
            _loadings.update {
                it.toMutableSet().apply { remove(Loadings.DEPT_PLU_ITEMS) }
            }
        }

    fun getEbtBalance(ebtType: EbtType) =
        viewModelScope.launch {
            if (_loadings.value.contains(Loadings.EBT_BALANCE)) {
                return@launch
            }
            _loadings.update { it.toMutableSet().apply { add(Loadings.EBT_BALANCE) } }
            val storeConfig = _storeConfig.value
            val storeLevelConfig = _storeLevelConfig.value
            val transaction = transaction()
            val epoch = Date().epochInSeconds()
            if (storeConfig == null || storeLevelConfig == null) {
                return@launch
            }
            val token =
                hOtpGenerator.generateHOTP(
                    tid = transaction.txnId,
                    token = storeConfig.token,
                    counter = storeConfig.posNumber.toLong()
                )
            val selectedWallet =
                if (ebtType == EbtType.FOOD_STAMP) {
                    EbtType.INQUIRY_FOOD
                } else {
                    EbtType.INQUIRY_CASH
                }
            if (storeLevelConfig.payfac == "epx") {
                processEbtWithEpx(ebtType, EpxTransactionType.INQUIRY, null)
                return@launch
            }
            if (storeLevelConfig.streamPayments) {
                val paymentInput =
                    PaymentInputDTO(
                        epoch,
                        TData(
                            token,
                            0,
                            transaction.txnId,
                            1,
                            TxnType.merchandise,
                            PaymentType.ebt,
                            0,
                            selectedWallet
                        )
                    )
                paymentProcessor.processPayment(
                    paymentInput,
                    storeConfig.posId,
                    cashierId()
                )
                    .collectLatest {
                        if (it.completed == true) {
                            this.cancel()
                            _loadings.update {
                                it.toMutableSet().apply {
                                    remove(Loadings.EBT_BALANCE)
                                }
                            }
                        }
                        if (it.isSuccess()) {
                            _uiState.update { ui ->
                                ui.copy(
                                    ebtBalancesCents =
                                    ui.ebtBalancesCents
                                        .apply {
                                            ui.ebtBalancesCents[
                                                ebtType] =
                                                it.info
                                                    ?.cardBalanceCents
                                        }
                                )
                            }
                            val ebtInfo =
                                EbtInfo(
                                    merchantId = it.info?.mid,
                                    auth = it.info?.payfacId,
                                    ref = it.info?.approvalCode,
                                    cardNo =
                                    it.info?.cardNumber,
                                    cardEntryMode =
                                    it.info
                                        ?.cardEntryMode,
                                    balanceInCents =
                                    (it.info
                                        ?.cardBalanceCents
                                        ?: 0f)
                                        .toInt(),
                                    ebtType = ebtType
                                )
                            printEbtBalance(ebtInfo)
                        }
                        if (it.isFailure()) {
                            showToast(
                                it.msg ?: it.title.orEmpty(),
                                ToastMessageType.Error
                            )
                        }
                    }
            }
        }

    fun handleCashAdjustment(numPadResult: NumPadResult) =
        viewModelScope.launch {
            if (uiState.value.cartItems.isEmpty()) {
                _cashDrawerOpenTime.emit(Date())
                try {
                    cashDrawerOpenCheck(
                        onOpenCallback = {
                            saveCashAdjustmentTxn(numPadResult)
                            _uiState.update {
                                it.copy(numPadResult = numPadResult)
                            }
                        }
                    )
                } catch (e: Exception) {
                    EventUtils.recordException(e)
                    showToast(
                        e.message ?: "Something went wrong",
                        ToastMessageType.Error
                    )
                }
            } else {
                _toastMessage.emit(
                    ToastMessage("Please complete the current transaction")
                )
            }
        }

    private fun printEbtBalance(ebtInfo: EbtInfo) =
        viewModelScope.launch {
            val storeConfig = _storeConfig.value ?: return@launch
            val transaction = transaction()
            val printBalanceInput =
                PrintBalanceInput(storeConfig, transaction.txnId, ebtInfo)
            printTransactionUseCase.printEbtBalance(printBalanceInput)
        }

    fun showCardPaymentDialog() {
        _uiState.update { it.copy(showCardPaymentDialog = true) }
    }

    fun hideCardPaymentDialog() {
        _uiState.update {
            it.copy(showCardPaymentDialog = false, showEbtPaymentDialog = false)
        }
    }

    fun showEbtPaymentDialog() {
        _uiState.update { it.copy(showEbtPaymentDialog = true) }
    }

    fun closeDay(triggerLogout: Boolean = false): Job =
        viewModelScope.launch {
            if (_loadings.value.contains(Loadings.CLOSING_DAY)) {
                return@launch
            }
            _storeConfig.value?.let {
                /**
                 * Verify that there are no pending fuel transactions i.e. fuel
                 * transactions with status FUEL_AUTHORIZED or FUEL_DISPENSED
                 */
                _loadings.update {
                    it.toMutableSet().apply { add(Loadings.CLOSING_DAY) }
                }
                val pendingFuelTxns =
                    transactionRepository.getPendingFuelCardTransactions()
                if (pendingFuelTxns.isNotEmpty()) {
                    _uiState.update { it.copy(showPendingFuelCardTxns = true) }
                    _loadings.update {
                        it.toMutableSet().apply {
                            remove(Loadings.CLOSING_DAY)
                        }
                    }
                    return@launch
                } else {
                    _uiState.update { it.copy(showPendingFuelCardTxns = false) }
                }
                val input =
                    DayCloseInputDto(
                        storeCode = it.storeCode,
                        posNo = it.posNumber,
                        posId = it.posId,
                        cashierId = cashierId()
                    )
                paymentUseCase
                    .closeEpxBatch(input)
                    .stateIn(viewModelScope)
                    .collect { result ->
                        when (result) {
                            is Result.Loading -> {
                                _loadings.update {
                                    it.toMutableSet().apply {
                                        add(
                                            Loadings.CLOSING_DAY
                                        )
                                    }
                                }
                            }

                            is Result.Error -> {
                                _loadings.update {
                                    it.toMutableSet().apply {
                                        remove(
                                            Loadings.CLOSING_DAY
                                        )
                                    }
                                }
                                _uiState.update { state ->
                                    state.copy(
                                        errorDialogMessage =
                                        result.errorMessage
                                    )
                                }
                            }

                            is Result.Success -> {
                                _loadings.update {
                                    it.toMutableSet().apply {
                                        remove(
                                            Loadings.CLOSING_DAY
                                        )
                                    }
                                }
                                showToast(
                                    ToastMessage(
                                        "Successfully closed the day."
                                    )
                                )
                                if (triggerLogout) {
                                    onLogOutClick(
                                        skipBatchClose =
                                        true
                                    )
                                }
                            }
                        }
                    }
            }
        }

    private fun setupPendingFuelTxnListener() =
        viewModelScope.launch {
            transactionRepository.getPendingFuelTransactionsFlow().collect { txns ->
                val cardTxns = txns.filter { it.hasCardPayment() }
                _pendingFuelTxns.emit(cardTxns)
            }
        }

    fun clearPendingFuelTxnFromUi() {
        viewModelScope.launch {
            _uiState.update { state -> state.copy(showPendingFuelCardTxns = false) }
        }
    }

    fun closePendingTransactions(txns: List<SaleTransaction>) =
        viewModelScope.launch {
            if (_loadings.value.contains(Loadings.CLOSING_TXNS)) {
                return@launch
            }
            _loadings.update { it.toMutableSet().apply { add(Loadings.CLOSING_TXNS) } }
            var closedAnyTxn = false
            for (txn in txns) {
                val closed = pendingFuelTxnUseCase.closePendingFuelTxn(txn)
                if (closed) {
                    closedAnyTxn = true
                }
            }
            if (closedAnyTxn) {
                closeDay()
            }
            _loadings.update {
                it.toMutableSet().apply { remove(Loadings.CLOSING_TXNS) }
            }
        }

    fun showPbSearchDialog() {
        _uiState.update { it.copy(showPbSearchDialog = true) }
    }

    fun hidePbSearchDialog() {
        _uiState.update { it.copy(showPbSearchDialog = false) }
    }

    fun onPbSearchItemClick(storeCode: String, item: PricebookSearchItem) =
        viewModelScope.launch {
            priceBookRepository.getPluItem(storeCode, item.pluId, item.pluModifier)
                ?.let { onPriceBookItemClick(it) }
                ?: run { showToast(ToastMessage("Couldn't find Pricebook item")) }
        }

    fun showLotteryDetails() {
        _uiState.update { it.copy(showLotteryDetails = true) }
    }

    fun hideLotteryDetails() {
        _uiState.update { it.copy(showLotteryDetails = false) }
    }

    fun hideFuelPriceDiff() {
        _uiState.update { it.copy(fuelPriceDiff = 0.0) }
    }

    fun showCreditAccountsDialog() {
        _uiState.update { it.copy(showCreditAccounts = true) }
    }

    fun hideCreditAccountsDialog() {
        _uiState.update { it.copy(showCreditAccounts = false) }
    }

    fun askForAmount(fundOption: FundOption) {
        when (fundOption) {
            FundOption.CASH -> _uiState.update { it.copy(showFundThruCash = true) }
            FundOption.CARD -> _uiState.update { it.copy(showFundThruCard = true) }
            FundOption.CHECK -> _uiState.update { it.copy(showFundThruCheck = true) }
            FundOption.ADJUSTMENT ->
                _uiState.update { it.copy(showFundThruAdjustment = true) }
        }
    }

    fun dismissFundCashDialog() =
        viewModelScope.launch { _uiState.update { it.copy(showFundThruCash = false) } }

    fun dismissFundCardDialog() =
        viewModelScope.launch { _uiState.update { it.copy(showFundThruCard = false) } }

    fun dismissFundCheckDialog() =
        viewModelScope.launch { _uiState.update { it.copy(showFundThruCheck = false) } }

    fun dismissFundAdjustmentDialog() =
        viewModelScope.launch {
            _uiState.update { it.copy(showFundThruAdjustment = false) }
        }

    fun onCreditClick() {
        if (!isValidTransaction()) {
            return
        }
        _uiState.update { it.copy(showAccountSelection = true) }
    }

    fun dismissAccountSelectionDialog() {
        _uiState.update { it.copy(showAccountSelection = false) }
    }

    fun selectCreditAccount(account: CreditAccount) =
        viewModelScope.launch {
            dismissAccountSelectionDialog()
            _uiState.update {
                it.copy(
                    showCreditPaymentDialog = true,
                    selectedCreditAccount = account
                )
            }
        }

    fun hideCreditPaymentDialog() {
        _uiState.update { it.copy(showCreditPaymentDialog = false) }
    }

    fun handleCreditPayment(amount: Float) =
        viewModelScope.launch {
            if (!isValidTransaction()) {
                return@launch
            }
            hideCreditPaymentDialog()
            var uiStateRef = uiState.value
            uiStateRef.selectedCreditAccount?.let { creditAccount ->
                var transactionSummary = uiStateRef.transactionSummary
                val txnPayment = _txnPayment.value
                transactionSummary =
                    transactionSummary.copy(creditAmountCollected = amount)
                txnPayment[TxnPaymentType.Credit] =
                    CreditPayment(
                        amount = amount,
                        accountId = creditAccount.id,
                        accountName = creditAccount.name,
                        creditLimitCents = creditAccount.creditLimitCents,
                        outstandingAmountCents =
                        creditAccount.currentOutstandingCents.plus(
                            (amount * 100).toInt()
                        )
                    )
                _txnPayment.emit(txnPayment)
                _uiState.update { it.copy(transactionSummary = transactionSummary) }
                if (transactionSummary.pendingAmount() <= 0f) {
                    /*
                                           If the pending amount is negative, complete this transaction.
                                           If given cheque amount > pendingAmount, refund through cash, so need to update "change"
                                           in cashPayment.
                                        */
                    if (transactionSummary.change() > 0) {
                        val cashTxnPayment: CashPayment =
                            (txnPayment[TxnPaymentType.Cash] as?
                                    CashPayment)
                                ?: CashPayment()
                        txnPayment[TxnPaymentType.Cash] =
                            cashTxnPayment.copy(
                                change = transactionSummary.change()
                            )
                        _txnPayment.emit(txnPayment)
                        _uiState.update {
                            uiStateRef.copy(
                                creditPaymentState =
                                uiStateRef
                                    .creditPaymentState
                                    .copy(
                                        amount =
                                        amount,
                                        completed =
                                        false,
                                        received =
                                        true
                                    )
                            )
                        }
                        delay(500)
                        // Open the cash drawer to allow cashier to return
                        // the change
                        cashDrawerOpenCheck({
                            _uiState.update {
                                completeCashTransaction(
                                    transactionSummary
                                )
                            }
                        })
                    } else {
                        if (uiStateRef.cardPaymentState.amount > 0f) {
                            uiStateRef =
                                uiStateRef.copy(
                                    cardPaymentState =
                                    uiStateRef
                                        .cardPaymentState
                                        .copy(
                                            completed =
                                            true
                                        )
                                )
                        }
                        if (uiStateRef.cashPaymentState.amount > 0f) {
                            uiStateRef =
                                uiStateRef.copy(
                                    cashPaymentState =
                                    uiStateRef
                                        .cashPaymentState
                                        .copy(
                                            completed =
                                            true
                                        )
                                )
                        }
                        _uiState.update {
                            uiStateRef.copy(
                                creditPaymentState =
                                uiStateRef
                                    .creditPaymentState
                                    .copy(
                                        amount =
                                        amount,
                                        completed =
                                        true,
                                        received =
                                        true
                                    )
                            )
                        }
                        showToast(
                            "Txn saved successfully",
                            ToastMessageType.Success
                        )
                    }
                }
            }
        }

    fun retryCardPayment(transaction: SaleTransaction, fuelPump: FuelPumpState? = null) =
        viewModelScope.launch {
            if (_loadings.value.contains(Loadings.CLOSING_TXNS)) {
                return@launch
            }
            _loadings.update { it.toMutableSet().apply { add(Loadings.CLOSING_TXNS) } }
            // Maybe POST_AUTH wasn't a success, recheck the status of the transaction
            // or retry
            // the capture
            val saved = pendingFuelTxnUseCase.closePendingFuelTxn(transaction)
            if (saved && fuelPump != null) {
                _event.emit(ViewModelEvent.RefreshFuelTransactions(fuelPump))
            }
            _loadings.update {
                it.toMutableSet().apply { remove(Loadings.CLOSING_TXNS) }
            }
        }

    fun showWhatsNew() {
        _uiState.update { it.copy(showWhatsNew = true) }
    }

    fun hideWhatsNew() {
        _uiState.update { it.copy(showWhatsNew = false) }
    }
}
