package com.swiftsku.swiftpos.ui.dashboard

import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Scaffold
import androidx.compose.material.ScaffoldState
import androidx.compose.material.Text
import androidx.compose.material.rememberScaffoldState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusProperties
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.input.key.onKeyEvent
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.testTagsAsResourceId
import androidx.compose.ui.window.Dialog
import androidx.navigation.NavController
import androidx.work.WorkManager
import com.orhanobut.logger.Logger
import com.pax.poscomm.usb.utils.USBUtil
import com.swiftsku.swiftpos.data.model.CardPayment
import com.swiftsku.swiftpos.data.model.ToastMessage
import com.swiftsku.swiftpos.data.model.TransactionItemWithFuel
import com.swiftsku.swiftpos.data.model.allFuelDispensed
import com.swiftsku.swiftpos.data.model.generateTransactionId
import com.swiftsku.swiftpos.data.model.hasCardPayment
import com.swiftsku.swiftpos.data.model.isEpxCaptured
import com.swiftsku.swiftpos.data.model.itemName
import com.swiftsku.swiftpos.data.type.NumPadResult
import com.swiftsku.swiftpos.data.type.TxnPaymentType
import com.swiftsku.swiftpos.data.type.showDollar
import com.swiftsku.swiftpos.data.type.title
import com.swiftsku.swiftpos.extension.centsToDollar
import com.swiftsku.swiftpos.extension.isTrue
import com.swiftsku.swiftpos.extension.orDefault
import com.swiftsku.swiftpos.extension.toDollars
import com.swiftsku.swiftpos.services.payment.pax.PaxPaymentService
import com.swiftsku.swiftpos.services.workers.BatchCloseWorker
import com.swiftsku.swiftpos.ui.components.AppToast
import com.swiftsku.swiftpos.ui.components.Loader
import com.swiftsku.swiftpos.ui.components.OnActivityResumedAfterDelay
import com.swiftsku.swiftpos.ui.dashboard.credits.CreditsViewModel
import com.swiftsku.swiftpos.ui.dashboard.dialogs.AccountSelectionDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.AgeVerificationDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.BatchCloseConfirmation
import com.swiftsku.swiftpos.ui.dashboard.dialogs.CardPaymentDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.CheckDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.CouponDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.CreditAccountsDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.CreditPaymentDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.DeptItemsSection
import com.swiftsku.swiftpos.ui.dashboard.dialogs.EBTDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.FuelConfigDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.FuelPriceDiffDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.FundAdjustmentDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.FundCardDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.FundCashDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.FundOption
import com.swiftsku.swiftpos.ui.dashboard.dialogs.GUPCDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.GenericErrorDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.InfoDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.IpConfigInput
import com.swiftsku.swiftpos.ui.dashboard.dialogs.LinkedPluSelectionDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.LotteryPayoutDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.MenuItemsSection
import com.swiftsku.swiftpos.ui.dashboard.dialogs.NumericPad
import com.swiftsku.swiftpos.ui.dashboard.dialogs.PaymentProcessDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.PaymentRetryDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.PayoutDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.PbSearchDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.PriceBookListDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.PriceCheck
import com.swiftsku.swiftpos.ui.dashboard.dialogs.RefundDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.RequestCashDrawerClose
import com.swiftsku.swiftpos.ui.dashboard.dialogs.SaveDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.UnderAgeDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.VendorDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.VoidConfirmationDialog
import com.swiftsku.swiftpos.ui.dashboard.dialogs.WhatsNewDialog
import com.swiftsku.swiftpos.ui.dashboard.drawer.Drawer
import com.swiftsku.swiftpos.ui.dashboard.main.state.allowAddingItemsToCart
import com.swiftsku.swiftpos.ui.dashboard.main.state.change
import com.swiftsku.swiftpos.ui.dashboard.main.state.grandTotal
import com.swiftsku.swiftpos.ui.dashboard.main.state.pendingAmount
import com.swiftsku.swiftpos.ui.dashboard.main.state.products
import com.swiftsku.swiftpos.ui.dashboard.main.state.pumps
import com.swiftsku.swiftpos.ui.dashboard.main.state.remainingAfterEBT
import com.swiftsku.swiftpos.ui.dashboard.main.state.selectedPump
import com.swiftsku.swiftpos.ui.dashboard.main.state.shouldShowProductsDialog
import com.swiftsku.swiftpos.ui.dashboard.main.state.showFuelDialog
import com.swiftsku.swiftpos.ui.dashboard.main.state.showFuelTransactionDialog
import com.swiftsku.swiftpos.ui.dashboard.main.state.totalEBT
import com.swiftsku.swiftpos.ui.dashboard.saletransaction.TxnHistoryDetailViewModel
import com.swiftsku.swiftpos.ui.dispenser.FDCViewModel
import com.swiftsku.swiftpos.ui.dispenser.FuelProductsDialog
import com.swiftsku.swiftpos.ui.dispenser.FuelPumpDialog
import com.swiftsku.swiftpos.ui.dispenser.transactions.FuelTransactions
import com.swiftsku.swiftpos.ui.dispenser.transactions.PendingFuelTransactions
import com.swiftsku.swiftpos.ui.navigation.Routes
import com.swiftsku.swiftpos.ui.presentation.CustomerPresentation
import com.swiftsku.swiftpos.ui.report.ReportDialog
import com.swiftsku.swiftpos.ui.report.ReportViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class, ExperimentalComposeUiApi::class)
@Composable
fun DashboardScreen(
    navController: NavController,
    dashboardVM: DashboardViewModel,
    txnHistoryDetailVM: TxnHistoryDetailViewModel,
    fdcVM: FDCViewModel,
    reportVM: ReportViewModel,
    creditsVM: CreditsViewModel,
    scaffoldState: ScaffoldState = rememberScaffoldState(),
    coroutineScope: CoroutineScope = rememberCoroutineScope(),
    secondaryDisplay: CustomerPresentation?,
) {

    val context = LocalContext.current
    OnActivityResumedAfterDelay { dashboardVM.onInfoTypeClick("Pax Terminal") }

    fun openTransactionDetail(transactionId: String) {
        navController.navigate("${Routes.TxnHistoryDetail.route}/$transactionId")
    }

    Scaffold(
        scaffoldState = scaffoldState,
        modifier = Modifier.statusBarsPadding()
    ) { contentPadding ->
        val currentTransaction by dashboardVM.currentTransaction.collectAsState()
        val focusRequester = remember { FocusRequester() }
        val uiState by dashboardVM.uiState.collectAsState()
        val storeLevelConfig by dashboardVM.storeLevelConfig.collectAsState()
        val storeConfig by dashboardVM.storeConfig.collectAsState()

        val toast by dashboardVM.toastMessage.collectAsState()
        val fuelToast by fdcVM.toastMessage.collectAsState()
        val creditToast by creditsVM.toastMessage.collectAsState()
        val freeFPState by fdcVM.freeFPState.collectAsState()
        val info by dashboardVM.info.collectAsState()
        val logOut by dashboardVM.logOutEvent.collectAsState()
        val fdcState by fdcVM.fdcState.collectAsState()
        val fdcUiState by fdcVM.uiState.collectAsState()
        val loadings by dashboardVM.loadings.collectAsState()
        val txnItems by dashboardVM.txnItems.collectAsState()
        val pendingFuelTxns by dashboardVM.pendingFuelTxns.collectAsState()

        val savedFuelTxn by dashboardVM.savedFuelTxn.collectAsState()

        LaunchedEffect(freeFPState) {
            freeFPState?.let {
                dashboardVM.onCartItemDelete(it)
                fdcVM.clearFPFreeState()
            }
        }

        LaunchedEffect(currentTransaction) {
            fdcVM.updateCurrentTransaction(currentTransaction)
        }

        LaunchedEffect(savedFuelTxn) {
            savedFuelTxn?.let {
                fdcVM.authorizePrepayPump(it.txn, it.txnPaymentType)
                dashboardVM.authorizedFuel()
            }
        }

        val event by dashboardVM.event.collectAsState()
        val selectedDepartment by dashboardVM.selectedDepartment.collectAsState()
        val selectedTransactionItem by dashboardVM.selectedTransactionItem.collectAsState()

        LaunchedEffect(event) {
            when (event) {
                DashboardViewModel.ViewModelEvent.AskUsbPermission -> {
                    USBUtil.requestUSBPermissionIfNeed(context)
                }

                is DashboardViewModel.ViewModelEvent.ScheduleEpxBatchClose -> {
                    (event as DashboardViewModel.ViewModelEvent.ScheduleEpxBatchClose).let {
                        PaxPaymentService.scheduleBatchClose(
                            context, it.pinpadConfig, it.storeCode,
                            it.posNo, it.posId, it.cashierId
                        )
                    }
                }

                DashboardViewModel.ViewModelEvent.CancelEpxBatchClose -> {
                    WorkManager.getInstance(context).cancelUniqueWork(BatchCloseWorker.WORKER_ID)
                }

                is DashboardViewModel.ViewModelEvent.RefreshFuelTransactions -> {
                    (event as DashboardViewModel.ViewModelEvent.RefreshFuelTransactions).let {
                        fdcVM.onViewTransactionsClick(it.fuelPump)
                    }
                }

                null -> {}
            }
        }

        LaunchedEffect(
            key1 = uiState.cartItems,
            key2 = uiState.transactionSummary,
            key3 = uiState.loyaltyState
        ) {
            if (secondaryDisplay != null) {
                secondaryDisplay.updateCartItems(uiState.cartItems)
                secondaryDisplay.updateCartSummary(uiState.transactionSummary)
                secondaryDisplay.updateOffers(
                    uiState.availableOffers,
                    uiState.loyaltyState.loyaltyId,
                    storeLevelConfig?.newOffersUi.isTrue()
                )
            }
        }


        LaunchedEffect(Unit) {
            dashboardVM.transactionScanned.collect { txnId ->
                navController.navigate("${Routes.TxnHistoryDetail.route}/$txnId")
            }
        }

        LaunchedEffect(uiState.showUserPhoneNumberInput) {
            if (uiState.showUserPhoneNumberInput) {
                secondaryDisplay?.getUserPhoneNumber()
            }
        }

        LaunchedEffect(logOut) {
            if (logOut) {
                dashboardVM.moveToDashboard(navController)
            }
        }

        LaunchedEffect(Unit) {
            focusRequester.requestFocus()
        }

        LaunchedEffect(toast, fuelToast, creditToast) {
            if (toast != null) {
                delay(2000)
                dashboardVM.clearToast()
            }
            Logger.d("Fuel Toast: $fuelToast")
            if (fuelToast != null) {
                delay(2000)
                fdcVM.clearToast()
            }
            if (creditToast != null) {
                delay(2000)
                creditsVM.clearToast()
            }
        }

        Box(
            modifier = Modifier
                .padding(contentPadding)
                .focusable()
                .focusRequester(focusRequester)
                .focusProperties { canFocus = true }
                .onKeyEvent { keyEvent ->
                    dashboardVM.onKeyEvent(keyEvent)
                    true
                }
                .semantics { testTagsAsResourceId = true }

        ) {
            DashboardContent(
                onCardClick = { dashboardVM.onCardClick(null, fdcState) },
                onEBTClick = { dashboardVM.onEBTClick() },
                onNoSaleClick = { dashboardVM.onNoSaleClick() },
                onRedeemClick = { dashboardVM.onRedeemClick() },
                onSaveClick = { dashboardVM.showSaveDialog() },
                onVoidClick = {
                    dashboardVM.onVoidClick()
                    /**
                     * Free fuel pump if there is any reserved.
                     */
                    val fuelItems = txnItems.filterIsInstance<TransactionItemWithFuel>()
                    if (fuelItems.isNotEmpty()) {
                        fdcVM.freeFuelPumps(fuelItems)
                    }
                },
                onCashClick = { dashboardVM.onCashClick() },
                onLoyaltyClick = { dashboardVM.onLoyaltyClick() },
                onCashAmountClick = { dashboardVM.onCashAmountClick(it) },
                onPriceBookItemClick = { dashboardVM.onPriceBookItemClick(it) },
                totalSale = uiState.totalSales,
                customerCount = uiState.customerCount,
                onDeleteClick = {
                    if (it is TransactionItemWithFuel) {
                        fdcVM.onCartItemDelete(it)
                    } else {
                        dashboardVM.onCartItemDelete(it)
                    }
                },
                onEditClick = { dashboardVM.onCartItemEdit(it) },
                onPriceCheckClick = { dashboardVM.onPriceCheckClick() },
                onRecallClick = { dashboardVM.onRecallClick(navController) },
                onTxnHistoryClick = { dashboardVM.onTxnHistoryClick(navController) },
                priceBookItems = uiState.priceBookList,
                departmentList = uiState.departmentList,
                cartItems = uiState.cartItems,
                onDepartmentClick = { dashboardVM.onDepartmentClick(it) },
                transactionSummary = uiState.transactionSummary,
                onMenuClick = {
                    coroutineScope.launch { scaffoldState.drawerState.open() }
                },
                coupons = uiState.coupons,
                onCouponRemoveClick = { dashboardVM.onRemoveCouponClick() },
                onPayoutClick = { dashboardVM.onPayoutClick() },
                onReportClick = { dashboardVM.onReportClick() },
                loyaltyState = uiState.loyaltyState,
                onInfoClick = { dashboardVM.onInfoClick() },
                onApplyEBTClick = { dashboardVM.onApplyEBTClick(it) },
                fuelPumps = fdcState.pumps()
                    .filter { storeConfig?.fuelConfig?.hidePumpIds?.contains(it.deviceId) != true },
                onPumpClick = fdcVM.onPumpClick,
                allowEditingCart = uiState.allowAddingItemsToCart,
                dashboardVM = dashboardVM,
                fdcVM = fdcVM
            )

            if (fdcUiState.showFuelDialog()) {
                val selectedPump = fdcState.selectedPump(
                    fdcUiState.selectedPump!!.pumpNo,
                    fdcUiState.selectedPump!!.deviceId
                )
                if (selectedPump != null) {
                    FuelPumpDialog(
                        fpState = selectedPump,
                        onDismissRequest = { fdcVM.onFuelPumpDialogDismiss() },
                        onPresetClick = { fdcVM.onFuelPresetClick() },
                        onResetInGasClick = {
                            fdcVM.onFuelPumpDialogDismiss(keepSelectedState = true)
                            dashboardVM.numberPadHelper(NumPadResult.RestInGas)
                        },
                        onPrepayClick = {
                            fdcVM.onFuelPumpDialogDismiss(keepSelectedState = true)
                            dashboardVM.numberPadHelper(NumPadResult.FuelPrepay)
                        },
                        onPrepayClickAmount = { fdcVM.onPrepayClickAmount(it, selectedPump) },
                        onMovePrepayClick = {
                            fdcVM.onFuelPumpDialogDismiss(keepSelectedState = true)
                            dashboardVM.numberPadHelper(NumPadResult.MovePrepay)
                        },
                        onWatchPumpClick = { fdcVM.onWatchPumpClick(selectedPump) },
                        onReprintClick = { fdcVM.onReprintClick(selectedPump) },
                        onStopClick = { fdcVM.onStopPumpClick(selectedPump) },
                        onResumeClick = { fdcVM.onOpenPumpClick(selectedPump) },
                        onApprove = { fdcVM.onApprovePumpClick(selectedPump) },
                        onUnlockPump = { fdcVM.onUnlockPump(selectedPump) },
                        onViewTransactionsClick = { fdcVM.onViewTransactionsClick(selectedPump) },
                        fdcVM = fdcVM
                    )
                }
            }

            if (fdcUiState.showFuelTransactionDialog()) {
                val selectedPump = fdcState.selectedPump(
                    fdcUiState.selectedPump!!.pumpNo,
                    fdcUiState.selectedPump!!.deviceId
                )
                if (selectedPump != null) {
                    FuelTransactions(
                        fuelPump = selectedPump,
                        onDismissRequest = { fdcVM.onFPTransactionDialogDismiss() },
                        onItemClick = {
                            Logger.d("Fuel Transaction Clicked: $it")
                        },
                        transactions = fdcUiState.transactions,
                        onRecall = {
                            val cardPayment = (it.txnPayment[TxnPaymentType.Card] as? CardPayment)
                            val epxCaptured = cardPayment.isEpxCaptured()
                            val allFuelDispensed = it.allFuelDispensed()
                            when {
                                it.hasCardPayment() && allFuelDispensed && !epxCaptured -> {
                                    dashboardVM.retryCardPayment(it, selectedPump)
                                }
                                allFuelDispensed -> {
                                    dashboardVM.recallTransaction(it)
                                    fdcVM.onFPTransactionDialogDismiss()
                                }
                            }
                        },
                        fdcVM = fdcVM
                    )
                }
            }

            if (fdcUiState.shouldShowProductsDialog()) {
                FuelProductsDialog(
                    products = fdcState.products(),
                    onProductSelect = {
                        fdcVM.onProductSelect(it)
                        dashboardVM.numberPadHelper(NumPadResult.FuelPreset)
                    },
                    onDismissRequest = { fdcVM.onProductDialogDismiss() }
                )
            }

            if (uiState.showReportDialog) {
                ReportDialog(
                    coroutineScope = coroutineScope,
                    onDismissDialog = { dashboardVM.dismissReportDateDialog() },
                    reportVM = reportVM
                )
            }

            if (uiState.isPriceCheck) {
                PriceCheck(
                    onDismissRequest = { dashboardVM.onPriceCheckDismiss() },
                    onKeyEvent = { dashboardVM.onKeyEvent(it) },
                    pluItem = uiState.priceCheckItem,
                    onAddToCartClick = { dashboardVM.onPriceBookItemClick(it) }
                )
            }

            if (uiState.showPendingFuelCardTxns) {
                PendingFuelTransactions(
                    pendingFuelTxns,
                    dashboardVM = dashboardVM,
                    fdcVM = fdcVM
                )
            }

            uiState.errorDialogMessage?.let {
                GenericErrorDialog(
                    onDismissRequest = { dashboardVM.dismissErrorDialog() },
                    message = it
                )
            }
            if (uiState.paymentState.showPaymentDialog) {
                PaymentRetryDialog(
                    onDismissRequest = { dashboardVM.dismissPaymentRetryDialog() },
                    paymentState = uiState.paymentState,
                    onRetryClick = { dashboardVM.onCardClick(null, fdcState) }
                )
            }


            if (uiState.showAgeVerificationDialog) {
                AgeVerificationDialog(
                    onDismissRequest = { dashboardVM.dismissAgeVerificationDialog() },
                    onSubmitRequest = { dashboardVM.onCustomerAgeSubmit(it) },
                    onSkipRequest = { dashboardVM.onSkipAgeVerification() },
                    dashboardVM = dashboardVM
                )
            }

            if (uiState.customerMinimumAge > 0) {
                UnderAgeDialog(
                    onDismissRequest = { dashboardVM.dismissUnderAgeDialog() },
                    minimumAge = uiState.customerMinimumAge
                )
            }

            if (uiState.numPadResult != null) {
                val title = when (uiState.numPadResult) {
                    NumPadResult.AddDepartmentItem, NumPadResult.EditDepartmentPrice -> "Department Price - ${selectedDepartment?.departmentName}"
                    NumPadResult.EditPluQuantity -> "Item Quantity - ${selectedTransactionItem?.itemName()}"
                    NumPadResult.RestInGas -> "Enter cash given by customer"
                    else -> uiState.numPadResult!!.title()
                }
                NumericPad(
                    numPadResult = uiState.numPadResult!!,
                    onCancel = { dashboardVM.dismissNumPadDialog() },
                    onOkClick = {
                        if (uiState.numPadResult == NumPadResult.FuelPrepay) {
                            fdcUiState.selectedPump?.let { pump ->
                                if (it.centsToDollar().toDouble() > 0) {
                                    fdcVM.onPrepayClickAmount(
                                        it.centsToDollar().toDouble(),
                                        pump
                                    )
                                    fdcVM.onFuelPumpDialogDismiss()
                                    dashboardVM.dismissNumPadDialog()
                                }
                            }
                        } else if (uiState.numPadResult == NumPadResult.FuelPreset) {
                            fdcUiState.selectedPump?.let { pump ->
                                fdcVM.onPresetVolumeEnter(it / 100f, pump)
                                dashboardVM.dismissNumPadDialog()
                            }
                        } else if (uiState.numPadResult == NumPadResult.RestInGas) {
                            fdcUiState.selectedPump?.let { pump ->
                                val amount = it.centsToDollar() - uiState.transactionSummary.grandTotal()
                                if (amount > 0) {
                                    fdcVM.onRestInGasClick(pump, amount)
                                } else {
                                    dashboardVM.showToast(
                                        ToastMessage("Amount should be greater than the due amount.")
                                    )
                                }
                                dashboardVM.dismissNumPadDialog()
                            }
                        } else if (uiState.numPadResult == NumPadResult.MovePrepay) {
                            fdcUiState.selectedPump?.let { pump ->
                                fdcVM.onMovePrepayClick(pump, it.toInt())
                                dashboardVM.dismissNumPadDialog()
                            }
                        }
                        dashboardVM.onKeypadOkPress(it)
                    },
                    header = title,
                    showDollar = uiState.numPadResult!!.showDollar()
                )
            }

            if (uiState.requestToCloseCashDrawer) {
                RequestCashDrawerClose(
                    txnHistoryDetailVM = txnHistoryDetailVM,
                    onDismissRequest = { dashboardVM.onCashDrawerCloseClick() },
                    message = {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Text(
                                text = " ${uiState.transactionSummary.change().toDollars()} ",
                                style = MaterialTheme.typography.h6
                            )
                            Text(
                                text = "Please give change and close the drawer to complete the transaction."
                            )
                        }
                    }
                )
            }

            uiState.refundState?.let {
                RequestCashDrawerClose(
                    txnHistoryDetailVM = txnHistoryDetailVM,
                    onDismissRequest = { dashboardVM.completeRefundTxn() },
                    message = {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Text(
                                text = " ${it.refundAmount.toDollars()} ",
                                style = MaterialTheme.typography.h6
                            )
                            Text(
                                text = "Please give the refund amount and close the drawer to complete the transaction."
                            )
                        }
                    }
                )
            }

            uiState.selectedMenuKeyId?.let {
                MenuItemsSection(
                    onDismiss = { dashboardVM.clearMenuSelectedKey() },
                    it,
                    dashboardVM = dashboardVM
                )
            }

            uiState.selectedDeptForDetail?.let {
                DeptItemsSection(
                    onDismiss = { dashboardVM.clearSelectedDept() },
                    it,
                    dashboardVM = dashboardVM
                )
            }

            if (uiState.gUpcItem != null) {
                GUPCDialog(
                    onDismissRequest = { dashboardVM.dismissGUpcDialog() },
                    onSubmitRequest = {
                        dashboardVM.onPriceBookItemClick(it)
                        dashboardVM.onAddGupcItemClick(it)
                    },
                    pluItem = uiState.gUpcItem!!.pluItem,
                    taxRateList = uiState.taxList,
                    onReportClick = { dashboardVM.onReportIssueClick(it) },
                    showReport = uiState.gUpcItem!!.showReport,
                    onError = { dashboardVM.showToast(it) },
                    dashboardVM = dashboardVM
                )
            }

            if (uiState.payout.showPayoutDialog) {
                PayoutDialog(
                    onDismissRequest = { dashboardVM.dismissPayoutDialog() },
                    onSubmitRequest = { dashboardVM.onPayoutNextClick(it) },
                    payout = uiState.payout,
                    dashboardVM = dashboardVM
                )
            }

            if (uiState.showVendorDialog) {
                VendorDialog(
                    onDismissRequest = { dashboardVM.hideVendorDialog() },
                    onSubmitRequest = { dashboardVM.addNewVendor(it) },
                )
            }

            if (uiState.showEBTDialog) {
                EBTDialog(
                    onDismissRequest = { dashboardVM.dismissEBTDialog() },
                    ebtAmount = uiState.enteredEbtAmount ?: uiState.transactionSummary.totalEBT(),
                    remainingAmount = uiState.transactionSummary.remainingAfterEBT(),
                    onProceedClick = { dashboardVM.onEBTProceedClick(it) },
                    dashboardVM = dashboardVM,
                )
            }

            if (uiState.showRefundDialog) {
                RefundDialog(dashboardVM)
            }

            if (uiState.showBatchCloseConfirmation) {
                BatchCloseConfirmation(dashboardVM)
            }

            if (uiState.showCouponDialog) {
                CouponDialog(
                    onDismissRequest = { dashboardVM.dismissCouponDialog() },
                    onSubmitRequest = { dashboardVM.onCouponAvailable(it) },
                    transactionSummary = uiState.transactionSummary
                )
            }

            if (uiState.showCheckDialog) {
                CheckDialog(
                    onDismissRequest = { dashboardVM.dismissCheckDialog() },
                    onSubmitRequest = { number, amount ->
                        dashboardVM.onCheckAmountClick(number, amount)
                    },
                )
            }

            if (uiState.showFundThruCash) {
                FundCashDialog(
                    onDismissRequest = { dashboardVM.dismissFundCashDialog() },
                    onSubmitRequest = { amount, notes ->
                        creditsVM.createFundEntry(
                            FundOption.CASH, amount,
                            generateTransactionId(
                                storeCode = dashboardVM.storeConfig.value?.storeCode.orEmpty(),
                                posNumber = dashboardVM.storeConfig.value?.posNumber.orDefault("1")
                            ),
                            dashboardVM.storeConfig.value?.storeCode.orEmpty(),
                            notes
                        )
                        dashboardVM.cashDrawerOpenCheck({})
                    },
                )
            }

            if (uiState.showFundThruCard) {
                FundCardDialog(
                    onDismissRequest = { dashboardVM.dismissFundCardDialog() },
                    onSubmitRequest = { amount, notes ->
                        creditsVM.processCardPayment(amount, notes)
                        dashboardVM.cashDrawerOpenCheck({})
                    },
                )
            }

            if (uiState.showFundThruCheck) {
                CheckDialog(
                    onDismissRequest = { dashboardVM.dismissFundCheckDialog() },
                    onSubmitRequest = { _, _ -> },
                    onSubmitCents = { amount, number ->
                        creditsVM.createFundEntry(
                            FundOption.CHECK, amount,
                            generateTransactionId(
                                storeCode = dashboardVM.storeConfig.value?.storeCode.orEmpty(),
                                posNumber = dashboardVM.storeConfig.value?.posNumber.orDefault("1")
                            ),
                            dashboardVM.storeConfig.value?.storeCode.orEmpty(),
                            number
                        )
                    },
                )
            }

            if (uiState.showFundThruAdjustment) {
                FundAdjustmentDialog(
                    onDismissRequest = { dashboardVM.dismissFundAdjustmentDialog() },
                    onSubmitRequest = { amount, reason ->
                        creditsVM.createFundEntry(
                            FundOption.ADJUSTMENT, amount,
                            generateTransactionId(
                                storeCode = dashboardVM.storeConfig.value?.storeCode.orEmpty(),
                                posNumber = dashboardVM.storeConfig.value?.posNumber.orDefault("1")
                            ),
                            dashboardVM.storeConfig.value?.storeCode.orEmpty(),
                            reason
                        )
                    },
                )
            }

            if (uiState.showSaveDialog) {
                SaveDialog(
                    onDismissRequest = { dashboardVM.hideSaveDialog() },
                    onSubmitRequest = { dashboardVM.onSaveClick(it) },
                )
            }

            if (uiState.barcodePriceBookList.isNotEmpty()) {
                PriceBookListDialog(
                    onDismissRequest = { dashboardVM.dismissBarcodePriceBookList() },
                    onItemClick = { dashboardVM.onBarcodePriceBookSelect(it) },
                    priceBook = uiState.barcodePriceBookList
                )
            }

            uiState.paymentResponseWS?.let {
                PaymentProcessDialog(
                    onDismissRequest = { dashboardVM.clearPaymentStreamState() },
                    paymentResponse = it,
                    dashboardVM = dashboardVM
                )
            }

//            if (uiState.showReportDialog) {
//                EODReportDateDialog(
//                    onDismissRequest = { viewModel.dismissReportDateDialog() },
//                    onSubmitRequest = { viewModel.onEODReportDateSelected(it) })
//            }

            if (uiState.loading != null) {
                Dialog(onDismissRequest = {}) {
                    Loader(text = uiState.loading!!)
                }
            }

            when {
                loadings.contains(DashboardViewModel.Loadings.CLOSING_DAY) -> {
                    Dialog(onDismissRequest = {}) {
                        Loader(text = "Closing the day!")
                    }
                }
                loadings.contains(DashboardViewModel.Loadings.CLOSING_TXNS) -> {
                    Dialog(onDismissRequest = {}) {
                        Loader(text = "Closing Pending Transaction")
                    }
                }
            }

            if (uiState.showInfo) {
                InfoDialog(
                    info = info,
                    onDismiss = { dashboardVM.dismissInfoDialog() },
                    onInfoTitleClick = { dashboardVM.onInfoTitleClick(it) },
                    onInfoTypeClick = { dashboardVM.onInfoTypeClick(it) },
                    dashboardVM = dashboardVM
                )
            }

            if (uiState.showIpInput) {
                IpConfigInput(
                    dashboardVM = dashboardVM
                )
            }

            if (uiState.showFuelConfigs) {
                FuelConfigDialog(
                    dashboardVM = dashboardVM,
                    fdcVM = fdcVM,
                    onDismissRequest = { dashboardVM.dismissFuelConfigDialog() }
                )
            }

            if (uiState.showVoidConfirmation) {
                VoidConfirmationDialog(dashboardVM = dashboardVM, fdcVM = fdcVM)
            }

            if (uiState.showCardPaymentDialog) {
                CardPaymentDialog(
                    amountDue = uiState.transactionSummary.pendingAmount(),
                    dashboardVM = dashboardVM,
                    fdcVM = fdcVM
                )
            }
            if (uiState.showEbtPaymentDialog) {
                CardPaymentDialog(
                    header = "EBT payment",
                    amountDue = uiState.transactionSummary.totalEBT(),
                    isForEbt = true,
                    dashboardVM = dashboardVM,
                    fdcVM = fdcVM
                )
            }
            if (uiState.showCreditPaymentDialog && uiState.selectedCreditAccount != null) {
                CreditPaymentDialog(
                    amountDue = uiState.transactionSummary.pendingAmount(),
                    creditAccount = uiState.selectedCreditAccount!!,
                    dashboardVM = dashboardVM
                )
            }

            if (uiState.showPbSearchDialog) {
                PbSearchDialog(
                    dashboardVM = dashboardVM,
                )
            }

            if (uiState.showCreditAccounts) {
                CreditAccountsDialog(
                    dashboardVM = dashboardVM,
                    creditsVM = creditsVM,
                    openTxnDetail = ::openTransactionDetail
                )
            }

            if (uiState.showAccountSelection) {
                AccountSelectionDialog(dashboardVM = dashboardVM, creditsVM = creditsVM)
            }

            if (uiState.showLotteryDetails) {
                LotteryPayoutDialog(dashboardVM = dashboardVM)
            }

            if (uiState.fuelPriceDiff > 0) {
                FuelPriceDiffDialog(dashboardVM = dashboardVM, fdcVM = fdcVM)
            }

            if (uiState.linkedPluItems.isNotEmpty()) {
                LinkedPluSelectionDialog(dashboardVM)
            }

            if (scaffoldState.drawerState.isOpen) {
                Drawer(
                    modifier = Modifier.align(Alignment.CenterEnd),
                    onLogOutClick = {
                        coroutineScope.launch { scaffoldState.drawerState.close() }
                        dashboardVM.onLogOutClick()
                    },
                    onCashAdjustment = {
                        coroutineScope.launch { scaffoldState.drawerState.close() }
                        dashboardVM.handleCashAdjustment(it)
                    },
                    coroutineScope = coroutineScope,
                    drawerState = scaffoldState.drawerState,
                    dashboardVM = dashboardVM,
                    fdcVM = fdcVM
                )
            }

            if (uiState.showWhatsNew) {
                WhatsNewDialog(
                    onDismissRequest = { dashboardVM.hideWhatsNew() },
                    dashboardVM
                )
            }

            toast?.let {
                Dialog(onDismissRequest = {}) {
                    AppToast(it)
                }
            }
            fuelToast?.let {
                Dialog(onDismissRequest = {}) {
                    AppToast(it)
                }
            }
            creditToast?.let {
                Dialog(onDismissRequest = {}) {
                    AppToast(it)
                }
            }
        }
    }
}

