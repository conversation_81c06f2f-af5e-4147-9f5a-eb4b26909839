package com.swiftsku.swiftpos.ui.dashboard.main.state

import com.fdc.core.types.DeviceState
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.FPState
import com.swiftsku.swiftpos.data.model.FuelPrice
import com.swiftsku.swiftpos.data.model.FuelSaleTrxState
import com.swiftsku.swiftpos.data.model.FuelingStatus


const val FDC_MAX_PREPAY = 100.0 // USD

data class FDCState(
    val dispensers: List<DispenserState> = emptyList(),
    val fdcConfig: FDCConfig? = null,
    val fpLock: List<FPLockState> = emptyList(),
    val linkedTxnId: String? = null,
    val ongoingRequests: Set<DeviceRequest> = emptySet(),
    val heartbeatStatus: HeartbeatStatus = HeartbeatStatus.INITIAL
)

enum class HeartbeatStatus {
    INITIAL, SENT, RECEIVED, STOPPED
}

data class DeviceRequest(val deviceId: Int, val requestType: FDCRequestType) {
    fun getRequestLabel(): String {
        return when (requestType) {
            FDCRequestType.RESERVE_FUEL_POINT -> "Reserving"
            FDCRequestType.AUTHORIZE_FUEL_POINT -> "Authorising"
            FDCRequestType.UNLOCK_FUEL_POINT -> "Unlocking" // Use for both Free/Terminate FP
            FDCRequestType.CLEAR_FUEL_POINT -> "Clearing"
        }
    }
}

enum class FDCRequestType {
    RESERVE_FUEL_POINT,
    AUTHORIZE_FUEL_POINT,
    UNLOCK_FUEL_POINT,
    CLEAR_FUEL_POINT
}

fun FDCState.addOngoingRequest(deviceId: Int, requestType: FDCRequestType) =
    this.copy(ongoingRequests = ongoingRequests + DeviceRequest(deviceId, requestType))

fun FDCState.removeOngoingRequest(deviceId: Int, requestType: FDCRequestType) =
    this.copy(ongoingRequests = ongoingRequests - DeviceRequest(deviceId, requestType))

fun FDCState.hasOngoingRequest(deviceId: Int, requestType: FDCRequestType) =
    ongoingRequests.contains(DeviceRequest(deviceId, requestType))

fun FDCState.getFirstOngoingRequest(deviceId: Int) =
    ongoingRequests.lastOrNull { it.deviceId == deviceId }

fun FDCState.clearAllRequests() = this.copy(ongoingRequests = emptySet())

fun FDCState.selectedPump(
    pumpNo: Int,
    deviceId: Int
): FuelPumpState? = this
    .pumps()
    .find { it.pumpNo == pumpNo && it.deviceId == deviceId }


fun FDCState.selectFpLock(
    pumpNo: Int,
    deviceId: Int
): FPLockState? = this
    .fpLock
    .find {
        it.lockedPumpState.pumpNo == pumpNo && it.lockedPumpState.deviceId == deviceId
    }

fun FDCState.addFpLock(
    lock: FPLockState
): FDCState {
    val updatedFpLocks = this.fpLock.toMutableList().apply {
        val index = indexOfFirst { it.lockedPumpState.pumpNo == lock.lockedPumpState.pumpNo }
        if (index != -1) {
            this[index] = lock
        } else {
            add(lock)
        }
    }
    return this.copy(fpLock = updatedFpLocks)
}

fun FDCState.deletedFpLock(
    deviceId: Int
): FDCState = this.copy(
    fpLock = this
        .fpLock
        .filter { it.lockedPumpState.deviceId != deviceId }
)

data class DispenserState(
    val deviceId: Int,
    val fuelPump: List<FuelPumpState> = emptyList(),
    val product: List<ProductState> = emptyList()
)


data class ProductState(
    val fuelPrice: List<FuelPrice> = emptyList(),
    val productNo: Long,
    val productName: String
)


data class FuelPumpState(
    val pumpNo: Int,
    val deviceId: Int,
    val nozzle: List<NozzleState> = emptyList(),
    val currentFuelStatus: FuelingStatus? = null,
    val fpState: FPState? = null,
    val fuelSaleTrxState: FuelSaleTrxState? = null
)

val pumpDisabledStates = listOf(
    DeviceState.FDC_CLOSED,
    DeviceState.FDC_CONFIGURE,
    DeviceState.FDC_DISABLED,
    DeviceState.FDC_ERRORSTATE,
    DeviceState.FDC_INVALIDSTATE,
    DeviceState.FDC_OFFLINE,
    DeviceState.FDC_OUTOFORDER,
    DeviceState.FDC_SUSPENDED,
    DeviceState.FDC_SUSPENDED_STARTED,
    DeviceState.FDC_SUSPENDED_FUELLING
)

val FuelPumpState.disableMovePrepay: Boolean get() = this.fpState?.fpState?.deviceState != DeviceState.FDC_AUTHORISED || deviceIsDisabled

val FuelPumpState.disablePrepay: Boolean
    get() = this.fpState?.fpState?.deviceState != DeviceState.FDC_READY && this.fpState?.fpState?.deviceState != DeviceState.FDC_CALLING || deviceIsDisabled

val FuelPumpState.disableApprove: Boolean
    get() = this.fpState?.fpState?.deviceState != DeviceState.FDC_CALLING && this.fpState?.fpState?.deviceState != DeviceState.FDC_READY || deviceIsDisabled

val FuelPumpState.deviceIsDisabled: Boolean
    get() = (this.fpState?.fpState?.deviceState in pumpDisabledStates)

val FuelPumpState.disableUnlock: Boolean
    get() = this.fpState?.fpState?.deviceState != DeviceState.FDC_LOCKED && this.fpState?.fpState?.deviceState != DeviceState.FDC_AUTHORISED || deviceIsDisabled

val FuelPumpState.isFueling: Boolean
    get() = this.fpState?.fpState?.deviceState == DeviceState.FDC_FUELLING

val FuelPumpState.icon
    get():String = when (fpState?.fpState?.deviceState) {
        DeviceState.FDC_CLOSED, DeviceState.FDC_DISABLED,
        DeviceState.FDC_ERRORSTATE, DeviceState.FDC_INVALIDSTATE,
        DeviceState.FDC_OFFLINE, DeviceState.FDC_OUTOFORDER, DeviceState.FDC_SUSPENDED -> "FuelClosed"

        DeviceState.FDC_READY -> "FuelReady"
        DeviceState.FDC_STARTED, DeviceState.FDC_FUELLING -> "FuelingIcon"
        DeviceState.FDC_CALLING -> "FuelCalling"
        DeviceState.FDC_LOCKED, DeviceState.FDC_SUSPENDED_STARTED,
        DeviceState.FDC_SUSPENDED_FUELLING, DeviceState.FDC_CONFIGURE -> "FuelLocked"

        DeviceState.FDC_AUTHORISED -> "FuelReserved"
        DeviceState.FDC_REQUESTED -> "FuelRequested"
        else -> "FuelIdle"
    }

val FuelPumpState.dialogTitle: String
    get() = when (fpState?.fpState?.deviceState) {
        DeviceState.FDC_CLOSED -> "Pump Closed"
        DeviceState.FDC_CONFIGURE -> "Configuring Pump"
        DeviceState.FDC_DISABLED -> "Pump Disabled"
        DeviceState.FDC_ERRORSTATE -> "Error State"
        DeviceState.FDC_FUELLING -> "Fuelling"
        DeviceState.FDC_INVALIDSTATE -> "Invalid State"
        DeviceState.FDC_LOCKED -> "Pump Locked"
        DeviceState.FDC_OFFLINE -> "Pump Offline"
        DeviceState.FDC_OUTOFORDER -> "Pump Out Of Order"
        DeviceState.FDC_READY -> "Ready To Fuel"
        DeviceState.FDC_REQUESTED -> "Requesting Approval"
        DeviceState.FDC_STARTED -> "Pump Motor Started"
        DeviceState.FDC_SUSPENDED -> "Pump Stopped"
        DeviceState.FDC_CALLING -> "Requesting Approval"
        DeviceState.FDC_AUTHORISED -> "Pump Approved"
        DeviceState.FDC_SUSPENDED_STARTED -> "Pump Stopped"
        DeviceState.FDC_SUSPENDED_FUELLING -> "Pump Stopped"
        null -> "Unknown"
    }

data class NozzleState(
    val nozzleNo: String,
    val productNo: Long,
    val blendRatio: Int?,
    val productName: String,
)

fun FDCState.pumps() = this.dispensers.fold(mutableListOf<FuelPumpState>()) { list, acc ->
    list.apply { this.addAll(acc.fuelPump) }
}

fun FDCState.areAllPumpsReady(): Boolean {
    return this.pumps().none { it.fpState?.fpState?.deviceState != DeviceState.FDC_READY }
}

fun FDCState.productName(productNo: Long): String? =
    this.dispensers
        .flatMap { it.product }
        .find { it.productNo == productNo }
        ?.productName

fun FDCState.products() = this.dispensers.flatMap { it.product }

fun FDCState.product(productNo: Long) =
    this.dispensers.flatMap { it.product }.firstOrNull { it.productNo == productNo }

/**
 * Check if any fuel reserve request is still in progress
 */
fun FDCState.disableCard(): Boolean {
    return ongoingRequests.any { it.requestType == FDCRequestType.RESERVE_FUEL_POINT }
}