package com.swiftsku.swiftpos.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class StoreUsers(
    @SerialName("doc_type")
    val docType: String,
    @SerialName("config_type")
    val configType: String,
    @SerialName("store_code")
    val storeCode: List<String> = emptyList(),
    val users: Map<String, User> = emptyMap(),
    @SerialName("mop")
    val mop: List<String>? = listOf("Cash", "Card", "EBT"),
    val mandatoryIdCheck: Boolean? = false,
    val feeConfig: List<Fee>? = emptyList(),
    val showOffers: Boolean = false,
    val supportHours: String? = "",
    val payfac: String? = "epx",
    val streamPayments: Boolean = false,
    val ebtPayments: Boolean = false,
    val ebtOptions: List<EbtType> = emptyList(),
    val autoRetryPayments: Boolean = false,
    val newDashboardUi: Boolean = false,
    val quickLotteryFlow: Boolean = false,
    val confirmOnSuccessfulPayment: Boolean = true,
    val loyaltyInput: LoyaltyInput = LoyaltyInput.CASHIER,
    val maxCreditLimitCents: Int? = 0,
    val maxFuelPostpayLimitCents: Int? = 0,
    val newOffersUi: Boolean = false
)
