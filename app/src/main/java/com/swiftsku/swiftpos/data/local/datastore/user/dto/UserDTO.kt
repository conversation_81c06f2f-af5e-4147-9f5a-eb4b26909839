package com.swiftsku.swiftpos.data.local.datastore.user.dto

import com.swiftsku.swiftpos.data.local.datastore.user.User.UserProto

data class UserDTO(
    val loggedIn: Boolean = false,
    val userType: String,
    val firstName: String,
    val lastName: String,
    val username: String,
    val email: String,
)

fun UserDTO.isOwner(): <PERSON><PERSON><PERSON> {
    return userType.equals("owner", ignoreCase = true)
}

fun UserDTO.toProto(): UserProto {
    return UserProto.newBuilder()
        .setLoggedIn(loggedIn)
        .setUserType(userType)
        .setFirstName(firstName)
        .setLastName(lastName)
        .setUsername(username)
        .setEmail(email)
        .build()
}

fun UserProto.toDTO(): UserDTO {
    return UserDTO(
        loggedIn = loggedIn,
        userType = userType,
        firstName = firstName,
        lastName = lastName,
        username = username,
        email = email,
    )
}

enum class UserType(val value: String) {
    OWNER("owner"), CASHIER("cashier")
}
