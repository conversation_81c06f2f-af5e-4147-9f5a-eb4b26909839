package com.swiftsku.swiftpos.data.model

import androidx.compose.ui.graphics.Color
import com.couchbase.lite.ReplicatorActivityLevel
import com.fdc.core.types.FQMMessageType
import com.fdc.core.types.FQMMessageType.*
import com.swiftsku.swiftpos.BuildConfig
import com.swiftsku.swiftpos.services.network.NetworkStatus
import com.swiftsku.swiftpos.services.payment.pax.PaxConnectionResult
import com.swiftsku.swiftpos.services.replicator.ReplicatorStatus
import com.swiftsku.swiftpos.services.vpn.VPNStatus
import com.swiftsku.swiftpos.ui.dashboard.main.state.HeartbeatStatus
import com.swiftsku.swiftpos.ui.theme.Blue
import com.swiftsku.swiftpos.ui.theme.Red
import com.swiftsku.swiftpos.ui.theme.Teal
import kotlinx.serialization.Serializable

@Serializable
data class Info(
    val storeName: String,
    val storeCode: String,
    val address: String,
    val merchantId: String,
    val serialNumber: String,
    val supportPhone: String = "(*************",
    val operationHour: String = "7AM to 5PM CST",
    val printCashReceipt: String = "Disabled",
    val printReportOnLogout: String = "Enabled",
    val version: String = BuildConfig.VERSION_NAME,
    val buildNumber: String = BuildConfig.VERSION_CODE.toString(),
    val terminalId: String,
    val sgStatus: String = ReplicatorStatus.Unknown.name,
    val pendingCounts: String = ReplicatorStatus.Unknown.name,
    val networkStatus: String = NetworkStatus.Unknown.name,
    val vpnStatus: String = VPNStatus.Unknown.name,
    val fqmStatus: FQMMessageType? = null,
    val heartbeatStatus: HeartbeatStatus = HeartbeatStatus.INITIAL,
    val paxTerminalState: PaxConnectionResult? = null,
) {
    companion object {
        fun empty() = Info(
            storeName = "",
            storeCode = "",
            address = "",
            merchantId = "",
            serialNumber = "",
            supportPhone = "(*************",
            operationHour = "7AM to 5PM CST",
            terminalId = "",
            sgStatus = ReplicatorStatus.Unknown.name,
            pendingCounts = ReplicatorStatus.Unknown.name,
            networkStatus = NetworkStatus.Unknown.name,
            vpnStatus = VPNStatus.Unknown.name,
        )
    }
}

fun Info.fqmStatusName(): String {
    val suffix = if (heartbeatStatus == HeartbeatStatus.STOPPED) " (Heartbeat Stopped)" else ""
    val statusLabel = when (fqmStatus) {
        FDC_CONNECTION_REFUSED -> "Connection Refused"
        FDC_CONNECTION_BROKEN -> "Connection Broken"
        FDC_CONNECTED -> "Connected"
        null -> "Unknown"
    }
    return "$statusLabel$suffix"
}

fun Info.map() = mapOf(
    "Cloud Status ($pendingCounts)" to sgStatus,
    "FQM Status" to fqmStatusName(),
    "Store Code" to storeCode,
    "Serial Number" to serialNumber,
    "Version" to "$version.$buildNumber",
    "Terminal ID" to terminalId,
    "Print Cash Receipt" to printCashReceipt,
    "Print Report on Logout" to printReportOnLogout,
    "Store Name" to storeName,
    "Support Phone Number with hours of operation" to "$supportPhone\n$operationHour",
    "SS Merchant ID" to merchantId,
    "Address" to address,
    "Pax Terminal" to if (paxTerminalState is PaxConnectionResult.ConnectionSuccess) "Connected" else "Disconnected\nRetry"
)

val Info.fqmStatusColor
    get(): Color = when (fqmStatus) {
        FDC_CONNECTION_REFUSED -> Red
        FDC_CONNECTION_BROKEN -> Red
        FDC_CONNECTED -> Teal
        null -> Blue
    }

val Info.sgColor
    get(): Color = when (sgStatus) {
        ReplicatorActivityLevel.BUSY.name, ReplicatorActivityLevel.IDLE.name -> Teal
        ReplicatorActivityLevel.STOPPED.name, ReplicatorActivityLevel.OFFLINE.name, ReplicatorStatus.Unknown.name, ReplicatorStatus.Stopped.name -> Red
        else -> Blue
    }

