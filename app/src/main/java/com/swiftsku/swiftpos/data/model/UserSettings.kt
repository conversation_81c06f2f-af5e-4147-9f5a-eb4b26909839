package com.swiftsku.swiftpos.data.model

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class UserSettings(
    @SerialName("display_totals")
    val displayTotals: Boolean? = false,
    val displayCustomerCount: Boolean? = false,
    @SerialName("can_close_day")
    val canCloseDay: Boolean? = false,
    @SerialName("can_adjust_cash")
    val canAdjustCash: Boolean? = false,
    @SerialName("can_manage_credits")
    val canManageCredits: Boolean? = false,
)
