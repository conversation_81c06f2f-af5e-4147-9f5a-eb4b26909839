package com.swiftsku.swiftpos.domain.dispenser

import com.fdc.core.types.FQMMessageType
import com.fdc.core.types.POSReadyMessageType
import com.fdc.core.types.POSReadyPOSdataType
import com.swiftsku.fdc.core.di.usecases.core.FQMMessageListenerUseCase
import com.swiftsku.fdc.core.di.usecases.core.ListenForFDCReadyUseCase
import com.swiftsku.fdc.core.di.usecases.core.SendPOSReadyUseCase
import com.swiftsku.swiftpos.data.model.FDCConfig
import com.swiftsku.swiftpos.data.model.generatePumpRequestId
import com.swiftsku.swiftpos.extension.isForCurrentDevice
import com.swiftsku.swiftpos.extension.toPOSTimeStamp
import com.swiftsku.swiftpos.ui.dashboard.main.state.FDCState
import com.swiftsku.swiftpos.ui.dashboard.main.state.HeartbeatStatus
import com.swiftsku.swiftpos.utils.EventUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import javax.inject.Inject


class FDCHeartBeatInitializeUseCase @Inject constructor(
    private val listenForFDCReadyUseCase: ListenForFDCReadyUseCase,
    private val sendPOSReadyUseCase: SendPOSReadyUseCase,
    private val listenForFQMMessage: FQMMessageListenerUseCase,
) {
    private var heartbeatJob: Job? = null
    private var timeoutJob: Job? = null
    private var runnerJob: Job? = null
    private val fdcReadyTimeoutMs = 9_000L
    private val heartbeatPeriodMs = 10_000L

    operator fun invoke(
        scope: CoroutineScope,
        fdcConfig: FDCConfig,
        fdcState: MutableStateFlow<FDCState>
    ) {
        runnerJob?.cancel()
        runnerJob = scope.launch {
            launch {
                listenForFDCReadyUseCase.fdcReady.collectLatest {
                    if (it.isForCurrentDevice(workstationId = fdcConfig.workstationID)) {
                        // Cancel timeout timer when fdcReady is received
                        timeoutJob?.cancel()
                        timeoutJob = null
                        fdcState.update { it.copy(heartbeatStatus = HeartbeatStatus.RECEIVED) }
                    }
                }
            }

            // Listen for FQM messages
            launch {
                listenForFQMMessage.fqmMessage.collectLatest {
                    if (it.messageType != FQMMessageType.FDC_CONNECTED) {
                        // Cancel heartbeat and timeout timer if disconnected
                        heartbeatJob?.cancel()
                        heartbeatJob = null
                        timeoutJob?.cancel()
                        timeoutJob = null
                    }
                }
            }

            // Start heartbeat loop directly
            heartbeatJob = launch {
                while (isActive) {
                    fdcState.update { it.copy(heartbeatStatus = HeartbeatStatus.SENT) }

                    // Cancel any existing timeout timer
                    timeoutJob?.cancel()

                    // Start timeout timer for 9 seconds
                    timeoutJob = launch {
                        delay(fdcReadyTimeoutMs)
                        // If we reach here, fdcReady was not received within 9 seconds
                        fdcState.update { it.copy(heartbeatStatus = HeartbeatStatus.STOPPED) }
                        EventUtils.recordException(Exception("Fuel module: Didn't receive FDC ready in time."))
                    }

                    sendPOSReadyUseCase(
                        POSReadyMessageType().apply {
                            poSdata =
                                POSReadyPOSdataType().apply {
                                    messageID = generatePumpRequestId(0)
                                    applicationSender = fdcConfig.applicationSender
                                    workstationID = fdcConfig.workstationID
                                    posTimeStamp = LocalDateTime.now().toPOSTimeStamp()
                                }
                        }
                    )
                    delay(heartbeatPeriodMs)
                }
            }
        }
    }
}
