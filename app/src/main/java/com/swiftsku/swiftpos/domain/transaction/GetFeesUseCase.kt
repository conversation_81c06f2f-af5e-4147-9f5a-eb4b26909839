package com.swiftsku.swiftpos.domain.transaction

import com.swiftsku.swiftpos.data.model.AppliedFee
import com.swiftsku.swiftpos.data.model.Fee
import com.swiftsku.swiftpos.data.model.FeeApplicationType
import com.swiftsku.swiftpos.data.model.FeeType
import com.swiftsku.swiftpos.extension.sumOfFloats
import com.swiftsku.swiftpos.extension.to2Decimal
import com.swiftsku.swiftpos.extension.toDollars


object GetFeesUseCase {

    fun getCardProcessingFee(cardAmount: Float, fees: List<Fee>?): AppliedFee? {
        val cardFeeConfig = fees?.firstOrNull { it.type == FeeType.CARD_PROCESSING }
        cardFeeConfig?.let {
            if (it.enabled && cardAmount > 0f) {
                val feeAmount = when (it.applicationType) {
                    FeeApplicationType.PERCENT -> it.feePerc?.let { cardAmount * (it / 100) }
                    FeeApplicationType.FIXED -> it.feeFixed.toDollars()
                }
                val info = when (it.applicationType) {
                    FeeApplicationType.PERCENT -> "${it.feePerc?.to2Decimal()}%"
                    FeeApplicationType.FIXED -> it.feeFixed.toDollars().toDollars()
                }
                return feeAmount?.let {
                    AppliedFee(
                        type = FeeType.CARD_PROCESSING,
                        amount = it.to2Decimal(),
                        info = info
                    )
                }
            }
        }
        return null
    }

    fun getTotalAppliedFee(appliedFees: List<AppliedFee>): Float {
        return appliedFees.sumOfFloats { it.amount }.to2Decimal()
    }
}