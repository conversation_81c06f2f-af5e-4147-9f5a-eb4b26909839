package com.swiftsku.swiftpos.domain.info

import com.swiftsku.swiftpos.data.model.Info
import com.swiftsku.swiftpos.data.model.StoreConfig
import javax.inject.Inject

class GetInfoUseCase @Inject constructor() {

    suspend operator fun invoke(storeConfig: StoreConfig, previousInfo: Info): Info =
        previousInfo.copy(
            storeCode = storeConfig.storeCode,
            storeName = storeConfig.receiptInfo.storeName,
            address = storeConfig.receiptInfo.streetAddress,
            merchantId = storeConfig.mid,
            serialNumber = storeConfig.posId,
            terminalId = storeConfig.posNumber,
            printCashReceipt = if (storeConfig.receiptInfo.printCashReceipt) "Enabled" else "Disabled",
            printReportOnLogout = if (storeConfig.noReportPrintOnLogout == true) "Disabled" else "Enabled",
        )
}